global:
  scrape_interval: 15s # Default scrape interval
  evaluation_interval: 15s # How often to evaluate rules

scrape_configs:
  # 1. Scrape Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      # Use the service name defined in docker-compose
      - targets: ['prometheus:9090']

  # 2. Scrape Qdrant
  - job_name: 'qdrant'
    metrics_path: /metrics # Qdrant exposes metrics here by default
    scheme: https
    static_configs:
      # Use the service name and default port defined in docker-compose
      - targets: ['qdrant:6333']
    tls_config:
          # This allows Prometheus to connect even if the hostname 'qdrant' doesn't match the certificate's name 'maidalv.com'.
          insecure_skip_verify: true
    authorization:
      type: Bearer
      credentials: 2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ

  # 3. Scrape Node Exporter (Host OS Metrics)
  - job_name: 'node-exporter'
    static_configs:
      # Use the service name and default port (9100)
      - targets: ['node-exporter:9100']

  # 4. Scrape PostgreSQL Exporter
  - job_name: 'postgres-exporter'
    static_configs:
      # Use the service name and default port (9187)
      - targets: ['postgres-exporter:9187']

  # 5. Scrape cAdvisor (Container Metrics)
  - job_name: 'cadvisor'
    static_configs:
      # Use the service name and default port (8080)
      - targets: ['cadvisor:8080']

  # 6. Scrape Blackbox Exporter (Uptime/Downtime)
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]  # Look for a HTTP 2xx response.
    static_configs:
      - targets:
        - https://api.maidalv.com    # Target to probe
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox_exporter:9115  # The blackbox exporter's real hostname:port.

  # 7. Scrape Node Exporter on Vast.ai (Remote Host)
  - job_name: 'vast-node-exporter'
    scheme: https # Assuming Cloudflare Tunnel provides TLS
    static_configs:
      - targets: ['vast-metrics.maidalv.com'] # The public endpoint for the node exporter
    tls_config:
      # This is often needed when behind a proxy like Cloudflare,
      # which might present a cert for maidalv.com, not vast-metrics.maidalv.com specifically in some configs.
      # If your Cloudflare cert is a wildcard (*.maidalv.com), you might not need this.
      insecure_skip_verify: true

# Optional: Add alerting rules configuration here if using Prometheus Alertmanager
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets: ['alertmanager:9093'] # If you add an Alertmanager service
# rule_files:
#   - "alert.rules.yml"
 # IP/Patents/patent_db_uspc.py
"""
Patent Database Operations Module - USPC

This module provides functions to interact with the PostgreSQL database
for patent USPC data.
"""

import os
import psycopg2
import psycopg2.extras
import pandas as pd
import logging
from tqdm import tqdm
from patent_db_grant import get_db_connection

# Global cache for USPC definitions
_uspc_definitions_df = None


# Configure logging
log_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'patent_db.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def set_uspc_definitions_cache(df):
    """Sets the global USPC definitions DataFrame cache.
    This is primarily used by multiprocessing worker initializers."""
    global _uspc_definitions_df
    _uspc_definitions_df = df


def load_uspc_definitions_cache():
    """
    Load all USPC definitions from the database into a global pandas DataFrame cache.
    This significantly speeds up find_definition_id by avoiding repeated database queries.

    Memory optimizations:
    - Converts string fields to categorical for memory efficiency
    - Keeps id as string for UUID compatibility
    - Optimizes data types

    Returns:
        bool: True if cache loaded successfully, False otherwise
    """
    global _uspc_definitions_df

    if _uspc_definitions_df is not None:
        # logger.info("USPC definitions cache already loaded.")
        return True

    conn = None
    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection for loading USPC definitions cache.")
            return False

        # Only fetch the columns we need for lookups
        sql = """
            SELECT id, class, subclass
            FROM patents_uspc_definitions
            ORDER BY class, subclass
        """

        logger.info("Loading USPC definitions cache from database...")
        df = pd.read_sql_query(sql, conn)

        # Memory optimization: Calculate original memory usage
        original_memory_bytes = df.memory_usage(deep=True).sum()
        original_memory_mb = original_memory_bytes / (1024 * 1024)
        logger.info(f"Original USPC cache memory usage: {original_memory_mb:.2f} MB ({original_memory_bytes:,} bytes)")

        # Memory optimization: Convert class and subclass to categorical
        df['class'] = df['class'].astype('category')
        df['subclass'] = df['subclass'].astype('category')

        # Keep id as string for UUID compatibility
        df['id'] = df['id'].astype('string')

        # Calculate optimized memory usage
        optimized_memory_bytes = df.memory_usage(deep=True).sum()
        optimized_memory_mb = optimized_memory_bytes / (1024 * 1024)
        memory_reduction = ((original_memory_bytes - optimized_memory_bytes) / original_memory_bytes) * 100

        logger.info(f"Successfully loaded {len(df)} USPC definitions into cache.")
        logger.info(f"Optimized USPC cache memory usage: {optimized_memory_mb:.2f} MB ({optimized_memory_bytes:,} bytes)")
        logger.info(f"Memory reduction: {memory_reduction:.1f}% ({(original_memory_bytes - optimized_memory_bytes):,} bytes saved)")

        # Set the global variable after successful loading
        _uspc_definitions_df = df

        return True

    except Exception as e:
        logger.error(f"Error loading USPC definitions cache: {e}", exc_info=True)
        _uspc_definitions_df = None
        return False
    finally:
        if conn:
            conn.close()


def get_uspc_definition_id(uspc_class, uspc_subclass):
    """
    Get USPC definition ID from the cache for given class and subclass.

    Args:
        uspc_class (str): USPC class
        uspc_subclass (str): USPC subclass

    Returns:
        str: USPC definition ID if found, None otherwise
    """
    global _uspc_definitions_df

    if _uspc_definitions_df is None:
        logger.warning(f"Process {os.getpid()} - USPC definitions cache not loaded. Attempting to load...")
        if not load_uspc_definitions_cache():
            logger.error("Failed to load USPC definitions cache.")
            return None

    # Filter the dataframe for matching class and subclass
    mask = (_uspc_definitions_df['class'] == uspc_class) & (_uspc_definitions_df['subclass'] == uspc_subclass)
    matching_rows = _uspc_definitions_df[mask]

    if len(matching_rows) == 0:
        return None
    elif len(matching_rows) == 1:
        return matching_rows.iloc[0]['id']
    else:
        logger.warning(f"Multiple USPC definitions found for class='{uspc_class}', subclass='{uspc_subclass}'. Using first match.")
        return matching_rows.iloc[0]['id']


def validate_uspc_classification(uspc_class, uspc_subclass):
    """
    Validate if a USPC classification exists in the definitions cache.

    Args:
        uspc_class (str): USPC class
        uspc_subclass (str): USPC subclass

    Returns:
        bool: True if classification exists, False otherwise
    """
    return get_uspc_definition_id(uspc_class, uspc_subclass) is not None


def upsert_patent_uspc_assignments(patent_records: list, mode: str) -> int:
    """
    Upserts USPC (United States Patent Classification) assignments into the
    patents_uspc_assignments table. This table links patents
    to patents_uspc_definitions.

    Args:
        patent_records (list): A list of dictionaries, where each dictionary
                               represents a patent record. Each record is expected
                               to have a 'document_id' key and a 'uspc_classifications' key.
                               The 'uspc_classifications' key should map to a list of dictionaries,
                               each representing a single USPC classification with keys
                               like 'class', 'subclass', and 'type'.

    Returns:
        int: The number of USPC assignment entries successfully inserted.
    """
    if not patent_records:
        logger.info("No patent records provided for USPC assignment upsert.")
        return 0
    
    table_name = 'patents_uspc_assignments'
    patents_lookup_table = 'patents'
    if mode == 'all':
        table_name += '_all'
        patents_lookup_table += '_all'

    conn = None
    cursor = None
    inserted_assignments_count = 0
    all_assignments_to_insert = []
    assignment_table_name = "patents_uspc_assignments"
    patent_table_name = "patents"
    if mode == "all":
        assignment_table_name += "_all"
        patent_table_name += "_all"

    sql_insert_assignment = f"""
        INSERT INTO {assignment_table_name} (patents_id, uspc_id, type)
        VALUES (%s, %s, %s)
        ON CONFLICT (patents_id, uspc_id, type) DO NOTHING;
    """

    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection for USPC assignment upsert. Aborting.")
            return 0

        cursor = conn.cursor()

        # Phase 1: Load USPC Definitions cache
        if not load_uspc_definitions_cache():
            logger.error("Failed to load USPC definitions cache. Aborting USPC assignments.")
            if cursor: cursor.close()
            if conn: conn.close()
            return 0 # Abort if we can't get definitions

        if _uspc_definitions_df is None or len(_uspc_definitions_df) == 0:
            logger.warning(f"No USPC definitions found in cache. No assignments can be made.")
            # Proceed, main loop will find no uspc_definition_ids.

        # Phase 2: Process records in-memory and build assignment list
        print(f"Processing {len(patent_records)} patent records for USPC assignments...")
        for record in tqdm(patent_records, desc="Processing USPC assignments", unit="record"):
            patents_id = record.get('id') # Directly use 'id' from the record
            if not patents_id:
                document_id_for_log = record.get('document_id', 'N/A')
                logger.warning(f"Skipping USPC processing for a record due to missing 'id'. Document ID: '{document_id_for_log}'. Record: {str(record)[:100]}")
                continue

            # Handle the legacy 'uspc' tuple format
            legacy_uspc = record.get('uspc')
            if isinstance(legacy_uspc, tuple) and len(legacy_uspc) == 2:
                main_class, sub_class = legacy_uspc
                if main_class and sub_class:  # Only process if both parts are present
                    uspc_definition_id = get_uspc_definition_id(main_class, sub_class)

                    if uspc_definition_id:
                        # Add as 'main' type
                        all_assignments_to_insert.append((patents_id, uspc_definition_id, 'main'))
                    else:
                        logger.warning(
                            f"No USPC definition ID found for class='{main_class}', subclass='{sub_class}'. "
                            f"Skipping main assignment for document_id '{document_id_for_log}'."
                        )

            # Handle the new 'uspc_classifications' list format
            uspc_entries = record.get('uspc_classifications', [])
            if isinstance(uspc_entries, list):
                for uspc_entry in uspc_entries:
                    if not isinstance(uspc_entry, dict):
                        logger.warning(f"Skipping non-dictionary USPC entry for document_id '{document_id_for_log}'. Entry: {uspc_entry}")
                        continue

                    # Extract USPC components from uspc_entry
                    uspc_class = uspc_entry.get('class')
                    uspc_subclass = uspc_entry.get('subclass')
                    uspc_type = uspc_entry.get('type', 'extra')  # Default to 'extra' if not specified

                    if not all([uspc_class, uspc_subclass]):
                        logger.warning(f"Missing class or subclass in USPC entry for document_id '{document_id_for_log}'. Entry: {uspc_entry}")
                        continue  # Skip this specific USPC entry

                    uspc_definition_id = get_uspc_definition_id(uspc_class, uspc_subclass)

                    if not uspc_definition_id:
                        logger.warning(
                            f"No USPC definition ID found for class='{uspc_class}', subclass='{uspc_subclass}'. "
                            f"Skipping assignment for document_id '{document_id_for_log}'."
                        )
                        continue  # Skip this specific USPC entry

                    # patents_id is already confirmed to be valid at this point for the record
                    all_assignments_to_insert.append((patents_id, uspc_definition_id, uspc_type))

        if not all_assignments_to_insert:
            logger.info("No USPC assignments prepared for insertion after processing all records.")
        else:
            logger.info(f"Attempting to insert {len(all_assignments_to_insert)} USPC assignment entries.")

            psycopg2.extras.execute_batch(cursor, sql_insert_assignment, all_assignments_to_insert, page_size=1000)
            conn.commit()

            inserted_assignments_count = len(all_assignments_to_insert)
            logger.info(f"Successfully processed {inserted_assignments_count} USPC assignments for insertion (some may have been skipped due to conflict).")

    except psycopg2.DatabaseError as db_err:
        logger.error(f"Database error during USPC assignment upsert: {db_err}")
        if conn:
            conn.rollback()
        inserted_assignments_count = 0
    except Exception as e:
        logger.error(f"Unexpected error during USPC assignment upsert: {e}", exc_info=True)
        if conn:
            conn.rollback()
        inserted_assignments_count = 0
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
            # logger.info("Database connection closed after USPC assignment attempt.")

    return inserted_assignments_count


def upsert_uspc_definitions(uspc_data_list: list) -> int:
    """
    Upserts USPC (United States Patent Classification) definitions into the
    patents_uspc_definitions table.

    Args:
        uspc_data_list (list): A list of dictionaries, where each dictionary
                               represents a USPC definition record.
                               Expected keys: 'class', 'subclass', 'definition'.

    Returns:
        int: The number of records successfully prepared and attempted for upsert.
             Returns 0 if input is empty or connection fails.
    """
    if not uspc_data_list:
        logger.info("No USPC definition data provided for upsert.")
        return 0

    conn = None
    cursor = None
    processed_count = 0
    batch_values = []

    # Table: patents_uspc_definition
    # Columns: class (TEXT), subclass (TEXT), definition (TEXT)
    # PK: (class, subclass)
    columns = ['class', 'subclass', 'definition']
    column_names_str = ', '.join(columns)
    placeholders_str = ', '.join(['%s'] * len(columns))

    # On conflict on (class, subclass), update the definition
    conflict_target_str = 'class, subclass'
    update_set_str = "definition = EXCLUDED.definition"

    sql = f"""
    INSERT INTO patents_uspc_definitions ({column_names_str})
    VALUES ({placeholders_str})
    ON CONFLICT ({conflict_target_str})
    DO UPDATE SET {update_set_str};
    """
    logger.debug(f"Upsert USPC Definitions SQL: {sql}")

    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection for USPC definitions upsert. Aborting.")
            return 0

        cursor = conn.cursor()

        for item in uspc_data_list:
            if not isinstance(item, dict):
                logger.warning(f"Skipping non-dictionary item in uspc_data_list: {item}")
                continue

            row_values = (
                item.get('class'),
                item.get('subclass'),
                item.get('definition')
            )

            # Basic validation: ensure primary key components are present
            if not item.get('class') or not item.get('subclass'):
                 logger.warning(f"Skipping USPC definition due to missing class or subclass: {item}")
                 continue

            batch_values.append(row_values)

        if not batch_values:
            logger.info("No valid USPC definition data prepared for batch upsert after filtering.")
        else:
            psycopg2.extras.execute_batch(cursor, sql, batch_values, page_size=500)
            conn.commit()
            processed_count = len(batch_values)
            logger.info(f"Successfully prepared and attempted to upsert {processed_count} USPC definition records.")

    except psycopg2.DatabaseError as db_err:
        logger.error(f"Database error during USPC definition upsert: {db_err}")
        if conn:
            conn.rollback()
        processed_count = 0 # Reset count as batch failed
    except Exception as e:
        logger.error(f"Unexpected error during USPC definition upsert: {e}", exc_info=True)
        if conn:
            conn.rollback()
        processed_count = 0 # Reset count as batch failed
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
            # logger.info("Database connection closed after USPC definition upsert attempt.")

    return processed_count

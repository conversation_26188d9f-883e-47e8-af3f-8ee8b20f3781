import gc
import zlib, base64, json, os, cv2, time, fitz, zipfile, asyncio, shutil, io, re, time, multiprocessing
import pandas as pd
from PIL import Image
import tempfile
from multiprocessing import Manager

from DatabaseManagement.Connections import get_gz_connection, is_connection_alive
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from Alerts.PicturesProcessing.ProcessPicturesShared import convert_page_number_to_image
start_time = time.time()
from IP.Trademarks.USPTO_TSDR_API import TSDRApi, format_reg_number
if multiprocessing.current_process().name == 'MainProcess':
    print(f"                TrademarkAPI TSDRApi after  {time.time()-start_time:.2f} seconds")
from Common.Constants import local_ip_folder, nas_ip_folder, sem_task
from FileManagement.NAS import NASConnection

# Import the helper function from Trademark_API to avoid circular imports
# We'll import it when needed in the functions that use it

# ❌⚠️📥
async def save_trademark_to_database(formatted_reg_no, new_row, db_connection, current_trademark_cache_df): # Renamed trademark_db_df
    """
    Saves a new trademark to the database.
    
    Args:
        formatted_reg_no: Formatted registration number
        new_row: Dictionary containing trademark data
        db_connection: Database connection
        trademark_db_df: DataFrame cache of trademarks
        
    Returns:
        bool: True if save was successful
    """
    try:
        # Only save if we have meaningful data
        if not new_row.get("reg_no") or not new_row.get("ser_no"):
            print(f"⚠️ Insufficient data for trademark {formatted_reg_no}, not saving to database")
            return False
        
        # Create a copy for database storage, removing metadata which is not stored in DB
        db_row = new_row.copy()
        metadata = db_row.pop("metadata", None)  # Remove metadata if present
        
        # Convert list fields to JSON strings for MySQL
        for field in ["int_cls", "country_codes", "associated_marks", "plaintiff_ids"]:
            if field in db_row and isinstance(db_row[field], list):
                # Convert any NumPy int64 to native Python int
                if field in ["int_cls", "plaintiff_ids"] and db_row[field]:
                    db_row[field] = [int(val) for val in db_row[field]]
                db_row[field] = json.dumps(db_row[field])
            
        try:
            with db_connection.cursor() as cursor:
                # Check if all required fields are present
                required_fields = ["reg_no", "ser_no", "TRO", "applicant_name", "text", "int_cls", "date", 
                                "nb_suits", "country_codes", "associated_marks", "info_source", 
                                "image_source", "certificate_source", "plaintiff_ids"]
                
                # Set default values for missing fields
                for field in required_fields:
                    if field not in db_row or db_row[field] is None:
                        if field in ["int_cls", "country_codes", "associated_marks", "plaintiff_ids"]:
                            db_row[field] = json.dumps([])  # Empty JSON array
                        elif field in ["TRO"]:
                            db_row[field] = True
                        elif field in ["nb_suits"]:
                            db_row[field] = 0
                        else:
                            db_row[field] = ""
                
                cursor.execute(
                    "INSERT INTO tb_trademark (reg_no, ser_no, TRO, applicant_name, text, int_cls, date, nb_suits, country_codes, associated_marks, info_source, image_source, certificate_source, plaintiff_ids) "
                    "VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", 
                    (db_row["reg_no"], db_row["ser_no"], db_row["TRO"], db_row["applicant_name"], 
                     db_row["text"], db_row["int_cls"], db_row["date"], db_row["nb_suits"], 
                     db_row["country_codes"], db_row["associated_marks"], db_row["info_source"], 
                     db_row["image_source"], db_row["certificate_source"], db_row["plaintiff_ids"])
                )
                db_connection.commit()
                
            # Add to the dataframe cache
            db_row_for_df = db_row.copy()
            # Convert JSON strings back to lists for the DataFrame
            for field in ["int_cls", "country_codes", "associated_marks", "plaintiff_ids"]:
                if field in db_row_for_df and isinstance(db_row_for_df[field], str):
                    try:
                        db_row_for_df[field] = json.loads(db_row_for_df[field])
                    except json.JSONDecodeError:
                        db_row_for_df[field] = []
                
            # Add back metadata for the DataFrame version if it existed
            if metadata:
                db_row_for_df["metadata"] = metadata
                
            # Add new row to current_trademark_cache_df (which is relevant_trademarks_df or a copy)
            # This ensures that if the same trademark is processed again *within the same batch* due to some logic error,
            # it might be found. However, the primary check is against the initially fetched data.
            # This line's utility is reduced if current_trademark_cache_df is not used for re-checks within process_trademark.
            # For now, we'll keep it to mirror original behavior if the df was meant to be updated.
            # Consider removing if current_trademark_cache_df is strictly read-only after initial fetch.
            if current_trademark_cache_df is not None:
                 current_trademark_cache_df = pd.concat([current_trademark_cache_df, pd.DataFrame([db_row_for_df])], ignore_index=True)
            print(f"✅ Added trademark {formatted_reg_no} to database")
            return True
            
        except Exception as e:
            print(f"🔥 Error saving trademark to database: {str(e)}")
            return False
    except Exception as e:
        print(f"🔥 Error preparing trademark data for database: {str(e)}")
        return False

async def send_trademark_to_nas(formatted_reg_no, nas, local_ip_folder, nas_ip_folder, ser_no=None):
    """
    Sends a single trademark's files to the NAS.

    Args:
        formatted_reg_no: Formatted registration number
        nas: NASConnection object
        local_ip_folder: Local IP folder path
        nas_ip_folder: NAS IP folder path
        ser_no: Serial number (optional, for new certificate path structure)

    Returns:
        bool: True if transfer was successful
    """
    try:
        # Define local paths
        local_xml_path = os.path.join(local_ip_folder, "Trademarks", "XML", f"{formatted_reg_no}.xml")
        local_image_path = os.path.join(local_ip_folder, "Trademarks", "Images", f"{formatted_reg_no}.webp")
        # Use new certificate path structure
        from IP.Trademarks.Trademark_API import get_certificate_local_path
        certificate_base_folder = os.path.join(local_ip_folder, "Trademarks", "Certificates")
        local_certificate_path = get_certificate_local_path(certificate_base_folder, ser_no=ser_no, reg_no=formatted_reg_no)

        # Define NAS paths (keeping old structure for NAS for now)
        # TODO: Update NAS structure when ser_no is available in this function
        nas_xml_path = f"{nas_ip_folder}/Trademarks/XML/{formatted_reg_no}.xml"
        nas_image_path = f"{nas_ip_folder}/Trademarks/Images/{formatted_reg_no}.webp"
        nas_certificate_path = f"{nas_ip_folder}/Trademarks/Certificates/{formatted_reg_no}.webp"
        
        if os.path.exists(local_xml_path) and not nas.ssh_exists(nas_xml_path):
            await asyncio.to_thread(nas.transfer_file_with_scp, local_path=local_xml_path, remote_path=nas_xml_path, to_nas=True)
            print(f"☑️ Transferred trademark XML {formatted_reg_no} to NAS")
            
        if os.path.exists(local_image_path) and not nas.ssh_exists(nas_image_path):
            await asyncio.to_thread(nas.transfer_file_with_scp, local_path=local_image_path, remote_path=nas_image_path, to_nas=True)
            print(f"☑️ Transferred trademark IMAGE {formatted_reg_no} to NAS")
            
        if os.path.exists(local_certificate_path) and not nas.ssh_exists(nas_certificate_path):
            await asyncio.to_thread(nas.transfer_file_with_scp, local_path=local_certificate_path, remote_path=nas_certificate_path, to_nas=True)
            print(f"☑️ Transferred trademark CERTIFICATE {formatted_reg_no} to NAS")
            
        return True
    except Exception as e:
        print(f"🔥 Error sending trademark {formatted_reg_no} to NAS: {str(e)}")
        return False



async def update_trademark_plaintiff_ids(formatted_reg_no, plaintiff_id, db_connection, current_trademark_cache_df): # Renamed trademark_db_df
    """
    Updates the plaintiff_ids for a trademark in the database.
    
    Args:
        formatted_reg_no: Formatted registration number
        plaintiff_id: ID of the plaintiff to add
        db_connection: Database connection
        trademark_db_df: Optional DataFrame cache of trademarks to check if update is needed
        
    Returns:
        bool: True if update was successful
    """

    # The check against current_trademark_cache_df (originally trademark_db_df) for early exit
    # will now only check against the initially fetched relevant items.
    # This is acceptable as the main goal is to update the DB.
    row = pd.DataFrame() # Initialize as empty
    if current_trademark_cache_df is not None and not current_trademark_cache_df.empty and 'reg_no' in current_trademark_cache_df.columns:
        row = current_trademark_cache_df.loc[current_trademark_cache_df['reg_no'] == formatted_reg_no]
    # Convert plaintiff_id to regular Python int to avoid numpy type issues
    plaintiff_id_int = int(plaintiff_id)
    

    # First check if update is already needed
    if not row.empty:
        current_plaintiff_ids_list = []
        if isinstance(row['plaintiff_ids'], str):
            try:
                loaded_ids = json.loads(row['plaintiff_ids'])
                if isinstance(loaded_ids, list):
                    current_plaintiff_ids_list = loaded_ids
            except (json.JSONDecodeError, TypeError):
                pass # Keep it as empty list if parsing fails
        elif isinstance(row['plaintiff_ids'], list):
            current_plaintiff_ids_list = row['plaintiff_ids']
        
        current_plaintiff_ids_list = [int(id_val) for id_val in current_plaintiff_ids_list if id_val is not None]

        if plaintiff_id_int in current_plaintiff_ids_list:
            return True # Exit the function early as the ID is already present



    # If we got here (not already in the plaintif_ids of the dataframe), we need to update the database
    max_retries = 3
    retry_count = 0
    
    # Convert plaintiff_id to regular Python int to avoid numpy type issues
    current_plaintiff_ids = []
    
    while retry_count < max_retries:
        try:
            # Check if connection is alive and reconnect if needed

            # Check if connection is alive and reconnect if needed
            if db_connection is None or not is_connection_alive(db_connection):
                print(f"\033[93m ⚠️ MySQL connection not available. Attempting to reconnect...\033[0m")
                db_connection = get_gz_connection()
                if db_connection is None:
                    raise Exception("Failed to establish database connection")
            
            # Get current plaintiff_ids
            with db_connection.cursor() as cursor:
                cursor.execute(
                    "SELECT plaintiff_ids FROM tb_trademark WHERE reg_no = %s", 
                    (formatted_reg_no,)
                )
                result = cursor.fetchone()
                
                if result:
                    # Convert plaintiff_id to regular Python int to avoid numpy type issues
                    plaintiff_id_int = int(plaintiff_id)
                    # Parse the JSON string to get the list
                    try:
                        current_plaintiff_ids = json.loads(result[0]) if result[0] else []
                        current_plaintiff_ids = [int(id_val) for id_val in current_plaintiff_ids]
                    except (json.JSONDecodeError, TypeError):
                        current_plaintiff_ids = []
                    
                    # Check if plaintiff_id is already in the list
                    if plaintiff_id_int not in current_plaintiff_ids:
                        # Add the plaintiff_id to the list then convert to string
                        updated_plaintiff_ids = current_plaintiff_ids + [plaintiff_id_int]
                        updated_plaintiff_ids_json = json.dumps(updated_plaintiff_ids)
                        
                        # Update the database
                        cursor.execute(
                            "UPDATE tb_trademark SET plaintiff_ids = %s WHERE reg_no = %s",
                            (updated_plaintiff_ids_json, formatted_reg_no)
                        )
                        db_connection.commit()
                        
                        print(f"\033[92m ☑️ Updated plaintiff_ids for trademark {formatted_reg_no}\033[0m")
                    
                    # If we got here, operation was successful
                    return True
                return False  # Not found
            
            # If we got here, operation was successful, so break the retry loop
            return True
                    
        except Exception as e:
            retry_count += 1
            if retry_count < max_retries:
                print(f"\033[93m ⚠️ Error updating plaintiff_ids for trademark {formatted_reg_no}: {str(e)}. Retrying ({retry_count}/{max_retries})...\033[0m")
                time.sleep(2)  # Wait before retrying
            else:
                print(f"\033[91m 🔥 Error updating plaintiff_ids for trademark {formatted_reg_no}: {str(e)}. Max retries reached.\033[0m")
                return False


def get_trademark_from_NAS(formatted_reg_no, df_entry, nas, ser_no=None):
    """
    Retrieves trademark files from local storage or NAS if needed.

    Args:
        formatted_reg_no: Formatted registration number
        df_entry: Dataframe entry for the trademark
        nas: NASConnection object
        ser_no: Serial number (optional, for new certificate path structure)

    Returns:
        tuple: (found_bool, xml_path, image_path, certificate_path)
    """
    # Get files from local or NAS
    xml_local_path = os.path.join(local_ip_folder, "Trademarks", "XML", f"{formatted_reg_no}.xml")
    image_local_path = os.path.join(local_ip_folder, "Trademarks", "Images", f"{formatted_reg_no}.webp")
    # Use new certificate path structure
    from IP.Trademarks.Trademark_API import get_certificate_local_path
    certificate_base_folder = os.path.join(local_ip_folder, "Trademarks", "Certificates")
    certificate_local_path = get_certificate_local_path(certificate_base_folder, ser_no=ser_no, reg_no=formatted_reg_no)
    
    # Check if all required files exist locally
    xml_exists = os.path.exists(xml_local_path)
    image_exists = os.path.exists(image_local_path)
    certificate_exists = os.path.exists(certificate_local_path)
    
    # If both main files exist locally, return success
    if xml_exists and image_exists and certificate_exists:
        print(f"☑️ Trademark {formatted_reg_no} files found locally")
        return True, xml_local_path, image_local_path, certificate_local_path
    
    # If not, try to get from NAS
    files_transferred = False
    
    # Check which files we need to get from NAS
    nas_xml_path = f"{nas_ip_folder}/Trademarks/XML/{formatted_reg_no}.xml"
    nas_image_path = f"{nas_ip_folder}/Trademarks/Images/{formatted_reg_no}.webp"
    nas_certificate_path = f"{nas_ip_folder}/Trademarks/Certificates/{formatted_reg_no}.webp"
    
    # Create local directories if they don't exist
    os.makedirs(os.path.dirname(xml_local_path), exist_ok=True)
    os.makedirs(os.path.dirname(image_local_path), exist_ok=True)
    os.makedirs(os.path.dirname(certificate_local_path), exist_ok=True)
    
    # Transfer XML if needed
    if not xml_exists and nas.ssh_exists(nas_xml_path):
        nas.transfer_file_with_scp(
            local_path=xml_local_path,
            remote_path=nas_xml_path,
            to_nas=False
        )
        files_transferred = True
        xml_exists = True
    
    # Transfer image if needed
    if not image_exists and nas.ssh_exists(nas_image_path):
        nas.transfer_file_with_scp(
            local_path=image_local_path,
            remote_path=nas_image_path,
            to_nas=False
        )
        files_transferred = True
        image_exists = True
    
    # Transfer certificate if it exists
    if not certificate_exists and nas.ssh_exists(nas_certificate_path):
        nas.transfer_file_with_scp(
            local_path=certificate_local_path,
            remote_path=nas_certificate_path,
            to_nas=False
        )
        files_transferred = True
        certificate_exists = True
    
    if files_transferred:
        print(f"☑️ Retrieved trademark {formatted_reg_no} files from NAS")
    
    # Check if we have the essential files now
    if xml_exists and image_exists and certificate_exists:
        return True, xml_local_path, image_local_path, certificate_local_path
    
    print(f"❌ Could not find complete files for trademark {formatted_reg_no}")
    return False, None, None, None



def create_full_list_of_reg_no(df_cases):
    api_client = TSDRApi()
    nas = NASConnection()
    full_list_of_reg_no = []
    certificates_folder = os.path.join(local_ip_folder, "Trademarks", "CertificatesFromTRO_ALL")
    os.makedirs(certificates_folder, exist_ok=True)
    for index, row in df_cases.iterrows():
        plaintiff_id = row["plaintiff_id"]
        if "trademarks" in row["images"].keys():
            for key, value in row["images"]["trademarks"].items():
                for i, reg_no in enumerate(row["images"]["trademarks"][key]["reg_no"]):
                    if reg_no != "":
                        # !!!!! How to know which one is which?
                        formatted_reg_no = format_reg_number(reg_no, id_key='rn')

                        # destination = os.path.join(certificates_folder, f"{formatted_reg_no}.webp")
                        # if not os.path.exists(destination):
                        #     sanitized_case_name = sanitize_name(f"{pd.to_datetime(row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {row['docket']}")
                        #     source = os.path.join(local_case_folder, sanitized_case_name, "images", row["images"]["trademarks"][key]["full_filename"][i])
                            
                        #     if os.path.exists(source):
                        #         shutil.copy(source, destination)
                        #     else:
                        #         print(f"🔥 Error: Source file {source} does not exist => copying the folder from NAS")
                        #         nas.ssh_nas_to_local(f"{nas_case_folder}/{sanitized_case_name}", os.path.join(local_case_folder, sanitized_case_name))
                        #         if os.path.exists(source):
                        #             shutil.copy(source, destination)
                        #         else:
                        #             print(f"🔥🔥🔥 Error: Source file {source} does not exist after copying the folder from NAS")
                        
                        
                        # Add to the list of registration numbers with plaintiff_id
                        full_list_of_reg_no.append({"plaintiff_id": plaintiff_id, "reg_no": formatted_reg_no})

    # Remove duplicates based on reg_no while preserving the first occurrence
    seen_reg_nos = set()
    unique_list = []
    for item in full_list_of_reg_no:
        if item["reg_no"] not in seen_reg_nos:
            seen_reg_nos.add(item["reg_no"])
            unique_list.append(item)

    print(f"There are {len(unique_list)} trademarks to scrape.")
    return unique_list


if __name__ == "__main__":
    df_cases = get_table_from_GZ("tb_case", force_refresh=False)
    full_list_of_reg_no = create_full_list_of_reg_no(df_cases)
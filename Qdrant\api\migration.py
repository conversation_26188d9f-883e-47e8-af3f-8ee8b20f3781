import os
from qdrant_client import QdrantClient, models

# --- Configuration ---
QDRANT_URL = os.getenv("QDRANT_URL", "https://vectorstore1.maidalv.com:6333")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")

OLD_COLLECTION_NAME = "IP_Assets"
NEW_COLLECTION_NAME = "IP_Assets_Optimized"

# --- <PERSON>ript ---

def create_optimized_collection(client: QdrantClient):
    """
    Creates the new, optimized collection with HNSW indexing, quantization,
    and rescoring enabled.
    """
    print(f"Creating new collection: {NEW_COLLECTION_NAME}")
    
    client.recreate_collection(
        collection_name=NEW_COLLECTION_NAME,
        on_disk_payload=True,
        vectors_config={
            "siglip_vector": models.VectorParams(
                size=1024,
                distance=models.Distance.COSINE,
                on_disk=True,                     # Store original vectors on disk for rescoring
                quantization_config=models.ScalarQuantization(
                    scalar=models.ScalarQuantizationConfig(
                        type=models.ScalarType.INT8,
                        always_ram=True          # Keep quantized vectors in RAM for speed
                        # optional: quantile=0.99
                    )
                ),
            )
        },
        hnsw_config=models.HnswConfigDiff(
            m=32,
            ef_construct=200,
            full_scan_threshold=10_000,
            on_disk=False                       # Keep the HNSW index in RAM
        )
    )
    
    
    # Create a payload index on the 'ip_type' field
    client.create_payload_index(
        collection_name=NEW_COLLECTION_NAME,
        field_name="ip_type",
        field_schema=models.PayloadSchemaType.KEYWORD,
    )
    print("Collection created and payload index on 'ip_type' is set.")


def migrate_data(client: QdrantClient):
    """
    Scrolls through all points in the old collection and upserts them
    into the new collection in batches, skipping points that already exist.
    """
    print("Starting data migration...")

    # Fetch existing point IDs from the new collection to avoid re-migrating them.
    print(f"Fetching existing point IDs from '{NEW_COLLECTION_NAME}' to resume migration...")
    existing_ids = set()
    next_id_offset = None
    
    try:
        # Check if the collection exists and get the count
        collection_info = client.get_collection(collection_name=NEW_COLLECTION_NAME)
        total_existing_points = collection_info.points_count

        if total_existing_points > 0:
            # Loop until all existing points are fetched, to avoid an infinite loop at the end.
            while len(existing_ids) < total_existing_points:
                points, next_id_offset = client.scroll(
                    collection_name=NEW_COLLECTION_NAME,
                    limit=10_000,  # Use a larger batch size for fetching IDs
                    offset=next_id_offset,
                    with_payload=False,  # We only need the IDs
                    with_vectors=False,
                )

                if not points:
                    # Break if the scroll returns no points, as a safeguard.
                    break

                for point in points:
                    existing_ids.add(point.id)
                print(f"Fetched {len(existing_ids)}/{total_existing_points} existing IDs...", end="\r")

                if next_id_offset is None:
                    # Break if Qdrant indicates there are no more pages.
                    break
    except Exception as e:
        # This can happen if the collection doesn't exist yet, which is fine.
        print(f"\nCould not fetch existing points (collection might not exist yet): {e}")
        pass

    if existing_ids:
        print(f"\nFound {len(existing_ids)} existing points in '{NEW_COLLECTION_NAME}'. Will skip them.")
    else:
        print(f"\nCollection '{NEW_COLLECTION_NAME}' is empty or does not exist. Starting a fresh migration.")

    next_page_offset = None
    total_migrated_this_run = 0
    total_skipped_this_run = 0
    
    old_collection_info = client.get_collection(collection_name=OLD_COLLECTION_NAME)
    total_points_to_process = old_collection_info.points_count

    while True:
        points, next_page_offset = client.scroll(
            collection_name=OLD_COLLECTION_NAME,
            limit=10000,  # Process in batches of 1000
            offset=next_page_offset,
            with_payload=True,
            with_vectors=True
        )

        if not points:
            print("\nScroll finished. No more points to migrate.")
            break

        points_to_upsert = []
        batch_skipped = 0
        for point in points:
            if point.id in existing_ids:
                batch_skipped += 1
                continue

            vector = point.vector
            if isinstance(vector, dict) and 'siglip_vector' in vector:
                vector_data = vector['siglip_vector']
            else:
                print(f"\nWarning: Unexpected vector format for point {point.id}. Skipping.")
                continue

            points_to_upsert.append(
                models.PointStruct(
                    id=point.id,
                    vector={"siglip_vector": vector_data},
                    payload=point.payload
                )
            )
        
        total_skipped_this_run += batch_skipped
        
        if points_to_upsert:
            client.upsert(
                collection_name=NEW_COLLECTION_NAME,
                points=points_to_upsert,
                wait=True
            )
            total_migrated_this_run += len(points_to_upsert)
        
        processed_count = len(existing_ids) + total_migrated_this_run
        print(f"Migrating... Processed: {processed_count}/{total_points_to_process} | "
              f"Total Migrated: {total_migrated_this_run} | Total Skipped: {total_skipped_this_run}", end="\r")

        if next_page_offset is None:
            print("\nMigration complete.")
            break

if __name__ == "__main__":
    qdrant_client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, timeout=60)
    
    print("--- Qdrant Data Migration Script ---")
    print(f"This script will migrate data from '{OLD_COLLECTION_NAME}' to '{NEW_COLLECTION_NAME}'.")
    
    # Step 1: Create the new collection
    try:
        collection_info = qdrant_client.get_collection(collection_name=NEW_COLLECTION_NAME)
        print(f"Collection '{NEW_COLLECTION_NAME}' already exists. Skipping creation.")
    except Exception:
        create_optimized_collection(qdrant_client)

    # Step 2: Migrate the data
    migrate_data(qdrant_client)

    print("\n--- Migration Process Finished ---")
    print("Please update your application to use the new collection name:")
    print(f"'{NEW_COLLECTION_NAME}'")

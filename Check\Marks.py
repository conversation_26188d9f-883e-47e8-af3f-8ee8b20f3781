from Check.Data_Cache import get_tm_auto, get_tm_meta, norm

def get_all_matches(description: str):
    """Return a list of (start, end, metadata) for all matches."""
    text = norm(description)
    
    # Use getter functions to ensure data is loaded
    tm_auto = get_tm_auto()
    tm_meta = get_tm_meta()

    matches = []
    for end_idx, tm_id in tm_auto.iter(text):
        matched_word_normalized = tm_meta[tm_id][0] # Get the normalized mark text
        start_idx = end_idx - len(matched_word_normalized) + 1

        # Check for whole word boundaries
        is_start_boundary = (start_idx == 0) or (not text[start_idx - 1].isalnum())
        is_end_boundary = (end_idx == len(text) - 1) or (not text[end_idx + 1].isalnum())

        if is_start_boundary and is_end_boundary:
            matches.append((start_idx, end_idx, tm_meta[tm_id]))
    return matches

def get_matches_and_check_perfect(brand_name: str):
    """
    Efficiently finds all trademark matches and checks for a perfect, whole-word match
    in a single pass of the trademark automaton.

    Returns:
        A tuple containing:
        - A list of all matches found (start, end, metadata).
        - A boolean indicating if a perfect match for the entire brand_name was found.
    """
    normalized_brand = norm(brand_name)
    tm_auto = get_tm_auto()
    tm_meta = get_tm_meta()

    matches = []
    is_perfect = False
    for end_idx, tm_id in tm_auto.iter(normalized_brand):
        matched_word_normalized = tm_meta[tm_id][0]
        start_idx = end_idx - len(matched_word_normalized) + 1

        # Check for whole word boundaries
        is_start_boundary = (start_idx == 0) or (not normalized_brand[start_idx - 1].isalnum())
        is_end_boundary = (end_idx == len(normalized_brand) - 1) or (not normalized_brand[end_idx + 1].isalnum())

        if is_start_boundary and is_end_boundary:
            matches.append((start_idx, end_idx, tm_meta[tm_id]))
            # Check if this valid match is also a perfect match
            if start_idx == 0 and end_idx == len(normalized_brand) - 1:
                is_perfect = True
    
    return matches, is_perfect
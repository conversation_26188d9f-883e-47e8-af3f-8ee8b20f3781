### To add:
## A Trademark section: 
# How many trademark in DB without trademark in Qdrant
# How many trademark in qdrant without trademak in DB
# How many trademark in DB without trademark in SSD 
# How many trademark on SSD without trademark in DB
# How many trademark in DB without trademark in COS
# How many trademark in COS without trademark in DB
# How many file referenced in the cases_df dataframe are dead links (i.e. not on COS / SSD)
## A Patent section: 
# How many Patent in DB without Patent in Qdrant
# How many Patent in qdrant without Patent in DB
# How many Patent in DB without Patent in SSD 
# How many Patent on SSD without Patent in DB
# How many Patent file in qdrant without the same file in COS
# How many Patent file in COS without same file in qdrant
# Some of the statistics from get_statistics.py in patents_bulk (?)
### A Copyright section
# How many copyrights in cases_df vs in database
# How many copyights are from exhibit, google, google vision, TinEye, GenAI ?

import os, sys, json, zlib, base64
import pandas as pd
from datetime import datetime
from DatabaseManagement.ImportExport import get_table_from_GZ
from DatabaseManagement.Connections import get_gz_connection
from FileManagement.Tencent_COS import list_all_files_in_COS
from Common.Constants import court_mapping
from logdata import log_message

def collect_generate_report_metrics():
    """
    Collect metrics from the Generate_Report.py logic.

    Returns:
        List of dictionaries with metric data
    """
    try:
        log_message("Collecting Generate Report metrics...", level='INFO')

        # Get data from database
        df_cases = get_table_from_GZ("tb_case", force_refresh=True)
        df_cases_steps = get_table_from_GZ("tb_case_steps", force_refresh=True)
        df_plaintiffs = get_table_from_GZ("tb_plaintiff", force_refresh=True)

        metrics = []

        ### Cases Metrics
        # Cases no cause
        df_cases_scraped = df_cases[df_cases["cause"].notna()]
        cases_without_cause = len(df_cases) - len(df_cases_scraped)
        metrics.append({
            'metric_name': 'cases_without_cause',
            'metric_value': cases_without_cause,
            'details_preview': {'description': 'Cases that have not been scraped yet'}
        })

        # Cases no images
        df_no_images = df_cases_scraped[df_cases_scraped["images"] == {'trademarks': {}, 'patents': {}, 'copyrights': {}}]
        cases_without_images = len(df_no_images)
        metrics.append({
            'metric_name': 'cases_without_images',
            'metric_value': cases_without_images,
            'details_preview': {'description': 'Cases with no IP images found'}
        })

        # Cases no plaintiff_id in plaintiff_df
        df_cases_no_plaintiff_id = df_cases[~df_cases["plaintiff_id"].isin(df_plaintiffs["id"])]
        cases_without_plaintiff_id = len(df_cases_no_plaintiff_id)
        metrics.append({
            'metric_name': 'cases_without_plaintiff_id',
            'metric_value': cases_without_plaintiff_id,
            'details_preview': {'description': 'Cases with invalid plaintiff references'}
        })

        # Cases duplicates
        df_cases_duplicates = df_cases[df_cases.duplicated(subset=["docket", "date_filed"])]
        duplicate_cases = len(df_cases_duplicates)
        metrics.append({
            'metric_name': 'duplicate_cases',
            'metric_value': duplicate_cases,
            'details_preview': {'description': 'Cases with same docket and date filed'}
        })

        # Cases ai summary no translation
        def safe_parse_aisummary(x):
            try:
                if isinstance(x, str) and x.strip().startswith("{"):
                    return json.loads(x)
                return None
            except (json.JSONDecodeError, IndexError):
                return None

        aisummary = df_cases_scraped['aisummary'].apply(safe_parse_aisummary)
        df_cases_no_chinese_overview = df_cases_scraped[aisummary.isna()]
        cases_ai_summary_no_translation = len(df_cases_no_chinese_overview)
        metrics.append({
            'metric_name': 'cases_ai_summary_no_translation',
            'metric_value': cases_ai_summary_no_translation,
            'details_preview': {'description': 'Cases without Chinese AI summary translation'}
        })

        # Cases no steps
        df_cases_no_steps = df_cases_scraped[~df_cases_scraped["id"].isin(df_cases_steps["case_id"])]
        cases_without_steps = len(df_cases_no_steps)
        metrics.append({
            'metric_name': 'cases_without_steps',
            'metric_value': cases_without_steps,
            'details_preview': {'description': 'Cases without any procedural steps'}
        })

        # Cases court not in court_mapping
        df_cases_court_not_in_court_mapping = df_cases[~df_cases["court"].isin(court_mapping.values())]
        cases_court_not_in_mapping = len(df_cases_court_not_in_court_mapping)
        metrics.append({
            'metric_name': 'cases_court_not_in_mapping',
            'metric_value': cases_court_not_in_mapping,
            'details_preview': {'description': 'Cases with courts not in mapping'}
        })

        ### Plaintiffs Metrics
        # Duplicate plaintiffs
        df_plaintiffs_duplicates = df_plaintiffs[df_plaintiffs["plaintiff_name"].str.lower().duplicated(keep='first')]
        duplicate_plaintiffs = len(df_plaintiffs_duplicates)
        metrics.append({
            'metric_name': 'duplicate_plaintiffs',
            'metric_value': duplicate_plaintiffs,
            'details_preview': {'description': 'Plaintiffs with duplicate names'}
        })

        # Plaintiffs without cases
        df_plaintiffs_no_cases = df_plaintiffs[~df_plaintiffs["id"].isin(df_cases["plaintiff_id"])]
        plaintiffs_without_cases = len(df_plaintiffs_no_cases)
        metrics.append({
            'metric_name': 'plaintiffs_without_cases',
            'metric_value': plaintiffs_without_cases,
            'details_preview': {'description': 'Plaintiffs not associated with any cases'}
        })

        # Plaintiffs no overview
        df_plaintiffs_with_overview = df_plaintiffs[df_plaintiffs["plaintiff_overview"].notna()]
        plaintiffs_without_overview = len(df_plaintiffs) - len(df_plaintiffs_with_overview)
        metrics.append({
            'metric_name': 'plaintiffs_without_overview',
            'metric_value': plaintiffs_without_overview,
            'details_preview': {'description': 'Plaintiffs without overview information'}
        })

        # Plaintiffs no overview translation
        def safe_parse_plaintiff_overview(x):
            try:
                if isinstance(x, str) and x.strip().startswith("{"):
                    return json.loads(x)
                return None
            except (json.JSONDecodeError, ValueError):
                return None

        plaintiff_overview = df_plaintiffs["plaintiff_overview"].apply(safe_parse_plaintiff_overview)
        plaintiffs_overview_missing_translation = sum(plaintiff_overview.isna()) - plaintiffs_without_overview
        metrics.append({
            'metric_name': 'plaintiffs_without_chinese_overview',
            'metric_value': plaintiffs_overview_missing_translation,
            'details_preview': {'description': 'Plaintiffs without Chinese overview translation'}
        })

        ### Steps Metrics
        # Steps without case
        df_steps_no_case = df_cases_steps[~df_cases_steps["case_id"].isin(df_cases["id"])]
        steps_without_case = len(df_steps_no_case)
        metrics.append({
            'metric_name': 'steps_without_case',
            'metric_value': steps_without_case,
            'details_preview': {'description': 'Steps not associated with any case'}
        })

        # Duplicate steps
        df_steps_duplicates = df_cases_steps[df_cases_steps.duplicated(subset=["case_id", "step_nb"])]
        duplicate_steps = len(df_steps_duplicates)
        metrics.append({
            'metric_name': 'duplicate_steps',
            'metric_value': duplicate_steps,
            'details_preview': {'description': 'Steps with same case ID and step number'}
        })

        # Steps no translation
        df_steps_no_translation = df_cases_steps[df_cases_steps["proceeding_text_cn"].isna()]
        steps_without_translation = len(df_steps_no_translation)
        metrics.append({
            'metric_name': 'steps_without_translation',
            'metric_value': steps_without_translation,
            'details_preview': {'description': 'Steps without Chinese translation'}
        })

        # Steps with NULL translation
        df_steps_NULL_translation = df_cases_steps[df_cases_steps["proceeding_text_cn"] == "[NULL]"]
        steps_with_null_translation = len(df_steps_NULL_translation)
        metrics.append({
            'metric_name': 'steps_with_null_translation',
            'metric_value': steps_with_null_translation,
            'details_preview': {'description': 'Steps with [NULL] translation'}
        })

        log_message(f"Collected {len(metrics)} Generate Report metrics", level='INFO')
        return metrics

    except Exception as e:
        log_message(f"Error collecting Generate Report metrics: {str(e)}", level='ERROR')
        raise

def collect_pictures_cleanup_metrics():
    """
    Collect metrics from the Pictures_Clean_Up.py logic.

    Returns:
        List of dictionaries with metric data
    """
    try:
        log_message("Collecting Pictures Clean Up metrics...", level='INFO')

        metrics = []

        # Get cases data for images analysis
        conn = get_gz_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT id, plaintiff_id, images FROM tb_case")
        data = cursor.fetchall()
        df_cases = pd.DataFrame(data, columns=[desc[0] for desc in cursor.description])

        # Count corrupted image data
        corrupted_count = 0
        for index, row in df_cases.iterrows():
            try:
                images = row['images']
                decoded_images = json.loads(zlib.decompress(base64.b64decode(images)).decode('utf-8'))
            except (json.JSONDecodeError, Exception):
                corrupted_count += 1

        metrics.append({
            'metric_name': 'cases_with_corrupted_image_data',
            'metric_value': corrupted_count,
            'details_preview': {'description': 'Cases where the images field (JSON blob) is corrupted'}
        })

        # Get full list of pictures from database
        full_list_of_pictures = set()
        full_list_of_certificates = set()

        for index, row in df_cases.iterrows():
            try:
                plaintiff_id = row["plaintiff_id"]
                images = row['images']
                decoded_images = json.loads(zlib.decompress(base64.b64decode(images)).decode('utf-8'))

                for ip in ["patents", "trademarks", "copyrights"]:
                    if ip in decoded_images.keys():
                        for key, value in decoded_images[ip].items():
                            full_list_of_pictures.add((plaintiff_id, key))
                            if value.get("full_filename"):
                                for picture in value["full_filename"]:
                                    full_list_of_certificates.add((plaintiff_id, picture))
            except Exception:
                continue  # Skip corrupted entries

        # Get COS files and compare
        try:
            print("Listing all files in COS under plaintiff_images/...")
            all_cos_files = list_all_files_in_COS("plaintiff_images/")

            # Count files in COS not in DataFrame
            files_to_delete = set()
            for file in all_cos_files:
                filename = file.split("/")[-1]
                plaintiff_id_str = file.split("/")[1]
                try:
                    plaintiff_id = int(plaintiff_id_str)
                    if (plaintiff_id, filename) not in full_list_of_pictures and (plaintiff_id, filename) not in full_list_of_certificates:
                        files_to_delete.add(file)
                except (ValueError, IndexError):
                    continue

            metrics.append({
                'metric_name': 'pictures_in_cos_not_in_dataframe',
                'metric_value': len(files_to_delete),
                'details_preview': {'description': 'Pictures in COS that are not associated with any case'}
            })

            # Count cases with pictures missing from COS
            cases_to_reprocess = set()
            for plaintiff_id, picture in full_list_of_pictures:
                key = f'plaintiff_images/{int(plaintiff_id)}/high/{picture}'
                key2 = f'plaintiff_images/{int(plaintiff_id)}/low/{picture}'
                if key not in all_cos_files or key2 not in all_cos_files:
                    # Find case_id for this plaintiff_id and picture
                    cases_for_plaintiff = df_cases[df_cases["plaintiff_id"] == plaintiff_id]
                    for idx, case_row in cases_for_plaintiff.iterrows():
                        try:
                            images = case_row['images']
                            decoded_images = json.loads(zlib.decompress(base64.b64decode(images)).decode('utf-8'))
                            for ip in ["patents", "trademarks", "copyrights"]:
                                if ip in decoded_images.keys():
                                    for key_name, value in decoded_images[ip].items():
                                        if key_name == picture:
                                            cases_to_reprocess.add(case_row["id"])
                                            break
                        except Exception:
                            continue

            for plaintiff_id, picture in full_list_of_certificates:
                key = f'plaintiff_images/{int(plaintiff_id)}/high/{picture}'
                if key not in all_cos_files:
                    # Find case_id for this plaintiff_id and picture
                    cases_for_plaintiff = df_cases[df_cases["plaintiff_id"] == plaintiff_id]
                    for idx, case_row in cases_for_plaintiff.iterrows():
                        try:
                            images = case_row['images']
                            decoded_images = json.loads(zlib.decompress(base64.b64decode(images)).decode('utf-8'))
                            for ip in ["patents", "trademarks", "copyrights"]:
                                if ip in decoded_images.keys():
                                    for key_name, value in decoded_images[ip].items():
                                        if value.get("full_filename") and picture in value["full_filename"]:
                                            cases_to_reprocess.add(case_row["id"])
                                            break
                        except Exception:
                            continue

            metrics.append({
                'metric_name': 'cases_with_pictures_missing_from_cos',
                'metric_value': len(cases_to_reprocess),
                'details_preview': {'description': 'Cases with pictures missing from COS storage'}
            })

        except Exception as e:
            log_message(f"Error accessing COS for pictures metrics: {str(e)}", level='WARNING')
            # Add placeholder metrics if COS is not accessible
            metrics.extend([
                {
                    'metric_name': 'pictures_in_cos_not_in_dataframe',
                    'metric_value': 0,
                    'details_preview': {'description': 'COS not accessible', 'error': str(e)}
                },
                {
                    'metric_name': 'cases_with_pictures_missing_from_cos',
                    'metric_value': 0,
                    'details_preview': {'description': 'COS not accessible', 'error': str(e)}
                }
            ])

        cursor.close()
        conn.close()

        log_message(f"Collected {len(metrics)} Pictures Clean Up metrics", level='INFO')
        return metrics

    except Exception as e:
        log_message(f"Error collecting Pictures Clean Up metrics: {str(e)}", level='ERROR')
        raise

def collect_all_statistics():
    """
    Collect all statistics from various sources.

    Returns:
        List of all metric dictionaries
    """
    try:
        log_message("Starting comprehensive statistics collection...", level='INFO')

        all_metrics = []

        # Collect Generate Report metrics
        all_metrics.extend(collect_generate_report_metrics())

        # Collect Pictures Clean Up metrics
        all_metrics.extend(collect_pictures_cleanup_metrics())

        log_message(f"Collected total of {len(all_metrics)} metrics", level='INFO')
        return all_metrics

    except Exception as e:
        log_message(f"Error in comprehensive statistics collection: {str(e)}", level='ERROR')
        raise

document.addEventListener('DOMContentLoaded', function() {
    const tableBody = document.querySelector('#api-keys-table tbody');
    const addKeyForm = document.getElementById('add-key-form');

    // Fetch and display API keys on page load
    fetchApiKeys();

    // Handle form submission for adding a new key
    addKeyForm.addEventListener('submit', function(event) {
        event.preventDefault();
        const formData = new FormData(addKeyForm);
        const data = Object.fromEntries(formData.entries());

        fetch('/api/api_keys', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        })
        .then(response => response.json())
        .then(result => {
            if (result.status === 'success') {
                fetchApiKeys(); // Refresh the list
                addKeyForm.reset();
            } else {
                alert('Error adding key: ' + result.error);
            }
        })
        .catch(error => console.error('Error:', error));
    });

    // Function to fetch and render API keys
    function fetchApiKeys() {
        fetch('/api/api_keys')
            .then(response => response.json())
            .then(data => {
                tableBody.innerHTML = ''; // Clear existing rows
                data.forEach(key => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${key.api_key}</td>
                        <td>${key.client_name}</td>
                        <td>${key.client_id}</td>
                        <td>${new Date(key.created_at).toLocaleString()}</td>
                        <td>${key.total_usage}</td>
                        <td>${key.current_month_usage}</td>
                        <td>${key.previous_month_usage}</td>
                        <td>
                            <button class="btn danger small delete-btn" data-api-key="${key.api_key}" ${key.total_usage > 0 ? 'disabled' : ''}>Delete</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });

                // Add event listeners to delete buttons
                document.querySelectorAll('.delete-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const apiKey = this.dataset.apiKey;
                        if (confirm(`Are you sure you want to delete API key: ${apiKey}?`)) {
                            deleteApiKey(apiKey);
                        }
                    });
                });
            })
            .catch(error => console.error('Error:', error));
    }

    // Function to delete an API key
    function deleteApiKey(apiKey) {
        fetch(`/api/api_keys/${apiKey}`, {
            method: 'DELETE',
        })
        .then(response => response.json())
        .then(result => {
            if (result.status === 'success') {
                fetchApiKeys(); // Refresh the list
            } else {
                alert('Error deleting key: ' + result.error);
            }
        })
        .catch(error => console.error('Error:', error));
    }
});
import asyncio
import aiohttp
import os
import json
import zlib
import numpy as np
from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct, VectorParams, Distance, PointIdsList
from qdrant_client.http.models import UpdateStatus
from logdata import log_message
import base64
from Common.uuid_utils import generate_uuid



BATCH_SIZE = 50

class EmbeddingQueue:
    def __init__(self, max_concurrent=3, num_workers=10):
        self.queue = asyncio.Queue()
        self.semaphore = asyncio.Semaphore(max_concurrent)  # Limit concurrent API submissions
        self.client = QdrantClient(url=os.environ.get("QDRANT_URL"), api_key=os.environ.get("QDRANT_API_KEY"), timeout=30)
        self.collection_name = "IP_Assets"
        self.num_workers = num_workers
        self.workers = []
        self.shutdown_event = asyncio.Event()
        self._ensure_collection()

    async def drain(self):
        """Wait until queue is empty and all tasks are processed"""
        await self.queue.join()

    async def poll_for_embeddings(self, task_id, max_attempts=100, poll_interval=15):
        """
        Poll the /embedding_status/<task_id> endpoint until completion

        Args:
            task_id (str): The task ID to poll for
            max_attempts (int): Maximum number of polling attempts (default: 60 = 5 minutes)
            poll_interval (int): Seconds between polls (default: 5)

        Returns:
            dict or None: The embeddings result if successful, None if failed/timeout
        """
        API_BASE_URL = "https://api.maidalv.com"
        status_url = f"{API_BASE_URL}/embedding_status/{task_id}"

        for attempt in range(max_attempts):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(status_url) as resp:
                        if resp.status != 200:
                            log_message(f"Status API error for task {task_id}: {resp.status}", level='ERROR')
                            return None

                        result = await resp.json()
                        status = result.get('status')

                        if status == 'completed':
                            log_message(f"Task {task_id} completed successfully", level='DEBUG')
                            return result.get('result')
                        elif status == 'error':
                            log_message(f"Task {task_id} failed: {result.get('message', 'Unknown error')}", level='ERROR')
                            return None
                        elif status == 'processing':
                            # Continue polling
                            if attempt < max_attempts - 1:  # Don't sleep on last attempt
                                await asyncio.sleep(poll_interval)
                        else:
                            log_message(f"Unknown status for task {task_id}: {status}", level='WARNING')
                            return None

            except Exception as e:
                log_message(f"Error polling task {task_id}: {str(e)}", level='ERROR')
                if attempt < max_attempts - 1:
                    await asyncio.sleep(poll_interval)

        log_message(f"Task {task_id} timed out after {max_attempts * poll_interval} seconds", level='ERROR')
        return None
    
    def _ensure_collection(self):
        """Create collection if it doesn't exist"""
        collections = self.client.get_collections().collections
        if not any(c.name == self.collection_name for c in collections):
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(size=1024, distance=Distance.COSINE)
            )
            log_message(f"Created Qdrant collection: {self.collection_name}", level='INFO')
    
    
    async def process_batch(self, batch):
        """Process batch of images"""
        # Batch check for existing embeddings
        point_ids = [item[0] for item in batch]
        try:
            existing_points = self.client.retrieve(
                collection_name=self.collection_name,
                ids=point_ids,
                with_vectors=False
            )
            existing_ids = {point.id for point in existing_points}
        except Exception as e:
            log_message(f"Qdrant batch check failed: {str(e)}", level='ERROR')
            existing_ids = set()
        
        to_process = []
        for item in batch:
            point_id, image_path, ser_no = item
            if point_id not in existing_ids:
                to_process.append(item)
            # else:
            #     log_message(f"Skipping existing embedding: {ser_no}", level='DEBUG')
        
        if not to_process:
            return
        
        # Prepare API request
        API_URL = "https://api.maidalv.com/get_image_embeddings"
        API_KEY = os.getenv("API_BEARER_TOKEN")
        headers = {"Content-Type": "application/json"}
        
        images_base64 = []
        valid_items = []
        for point_id, ser_no in [(item[0], item[2]) for item in to_process]: # Extract point_id and ser_no
            image_path = next(item[1] for item in to_process if item[0] == point_id) # Find image_path
            if os.path.exists(image_path):
                try:
                    with open(image_path, "rb") as img_file:
                        images_base64.append(base64.b64encode(img_file.read()).decode('utf-8'))
                    valid_items.append((point_id, ser_no))
                except Exception as e:
                    log_message(f"Error reading {image_path}: {str(e)}", level='ERROR')
            else:
                log_message(f"Image missing: {image_path}", level='WARNING')
        
        if not images_base64:
            return
        
        payload = {
            "api_key": API_KEY,
            "images": images_base64
        }
        
        # Submit to API and handle async response
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(API_URL, json=payload, headers=headers) as resp:
                    if resp.status != 200:
                        log_message(f"API error: {resp.status} {await resp.text()}", level='ERROR')
                        return

                    result = await resp.json()
                    status = result.get('status')

                    if status == 'success':
                        # Handle legacy synchronous response (if API ever returns this)
                        await self._process_embeddings_result(result, valid_items)
                    elif status == 'processing':
                        # Handle new asynchronous response
                        task_id = result.get('task_id')
                        if not task_id:
                            log_message("No task_id received from API", level='ERROR')
                            return

                        log_message(f"Embedding task queued: {task_id}, polling for completion...", level='DEBUG')

                        # Poll for completion
                        embeddings_result = await self.poll_for_embeddings(task_id)
                        if embeddings_result:
                            await self._process_embeddings_result(embeddings_result, valid_items)
                        else:
                            log_message(f"Failed to get embeddings for task {task_id}", level='ERROR')
                    else:
                        log_message(f"Unexpected API response status: {status}", level='ERROR')

        except Exception as e:
            log_message(f"Embedding processing failed: {str(e)}", level='ERROR')

    async def _process_embeddings_result(self, embeddings_result, valid_items):
        """
        Process the embeddings result and store in Qdrant

        Args:
            embeddings_result (dict): The result containing embeddings
            valid_items (list): List of (point_id, ser_no) tuples
        """
        try:
            if embeddings_result.get('status') != 'success':
                log_message(f"Embeddings result error: {embeddings_result.get('message', 'Unknown error')}", level='ERROR')
                return

            # Decode and decompress embeddings
            encoded_compressed_embeddings = embeddings_result.get('embeddings')
            if not encoded_compressed_embeddings:
                log_message("No embeddings data in result", level='ERROR')
                return

            compressed_embeddings = base64.b64decode(encoded_compressed_embeddings)
            embeddings_bytes = zlib.decompress(compressed_embeddings)

            # Convert bytes back to numpy array
            embeddings = np.frombuffer(embeddings_bytes, dtype=np.float32).reshape(-1, 1024)

            if len(embeddings) != len(valid_items):
                log_message(f"Embedding count mismatch: expected {len(valid_items)}, got {len(embeddings)}", level='ERROR')
                return

            # Create points for Qdrant
            points = [
                PointStruct(
                    id=item[0],
                    vector={"siglip_vector": embedding.tolist()},  # Convert numpy array to list
                    payload={
                        "ip_type": "Trademark"
                    }
                )
                for item, embedding in zip(valid_items, embeddings)
            ]

            # Upload to Qdrant
            self.client.upsert(
                collection_name=self.collection_name,
                points=points,
                wait=True
            )

            queue_size = self.queue.qsize()
            log_message(f"Uploaded {len(points)} embeddings to Qdrant, {queue_size} items remaining in queue", level='INFO')

        except Exception as e:
            log_message(f"Error processing embeddings result: {str(e)}", level='ERROR')
    
    async def enqueue(self, ser_no, image_path):
        """Add image to processing queue (individual enqueue - use batch_enqueue for better performance)"""
        if not ser_no:
            log_message("Skipping item with missing ser_no", level='WARNING')
            return False

        point_id = generate_uuid(ser_no)
        await self.queue.put((point_id, image_path, ser_no))
        # log_message(f"Enqueued embedding for {ser_no}, queue size: {self.queue.qsize()}", level='DEBUG')
        return True

    async def batch_enqueue(self, items, batch_size=1000):
        """
        Efficiently enqueue multiple items after batch checking for existing embeddings

        Args:
            items: List of tuples (ser_no, image_path)
            batch_size: Size of batches for Qdrant existence checks

        Returns:
            tuple: (enqueued_count, skipped_count)
        """
        if not items:
            return 0, 0

        # Generate point IDs for all items
        items_with_ids = [(generate_uuid(ser_no), ser_no, image_path) for ser_no, image_path in items if ser_no]

        if not items_with_ids:
            log_message("No valid items to enqueue", level='WARNING')
            return 0, 0

        all_point_ids = [item[0] for item in items_with_ids]
        existing_ids = set()

        # Batch check for existing embeddings
        log_message(f"Checking existence of {len(all_point_ids)} embeddings in batches of {batch_size}", level='INFO')

        for i in range(0, len(all_point_ids), batch_size):
            batch_ids = all_point_ids[i:i + batch_size]
            try:
                existing_points = self.client.retrieve(
                    collection_name=self.collection_name,
                    ids=batch_ids,
                    with_vectors=False
                )
                existing_ids.update(point.id for point in existing_points)
            except Exception as e:
                log_message(f"Qdrant batch check failed for batch {i//batch_size + 1}: {str(e)}", level='WARNING')
                # Continue without adding to existing_ids if check fails

        # Enqueue only items that don't already exist
        enqueued_count = 0
        skipped_count = 0

        for point_id, ser_no, image_path in items_with_ids:
            if point_id not in existing_ids:
                await self.queue.put((point_id, image_path, ser_no))
                enqueued_count += 1
            else:
                skipped_count += 1

        log_message(f"✅ Batch enqueue completed: 📈 {enqueued_count} enqueued, 📊 {skipped_count} skipped (already exist)", level='INFO')
        return enqueued_count, skipped_count

    def get_queue_status(self):
        """Get current queue status for debugging"""
        return {
            'queue_size': self.queue.qsize(),
            'active_workers': len(self.workers),
            'shutdown_requested': self.shutdown_event.is_set()
        }
    
    async def worker(self, worker_id):
        """Process queue items in batches"""
        log_message(f"Embedding worker {worker_id} started", level='INFO')

        while not self.shutdown_event.is_set():
            batch = []
            batch_items = []

            # Collect up to BATCH_SIZE items for a batch, but don't wait too long
            try:
                # Wait for at least one item, but with timeout
                item = await asyncio.wait_for(self.queue.get(), timeout=5.0)
                batch.append(item)
                batch_items.append(item)

                # Try to get more items quickly to fill the batch
                while len(batch) < BATCH_SIZE:
                    try:
                        item = await asyncio.wait_for(self.queue.get(), timeout=0.1)
                        batch.append(item)
                        batch_items.append(item)
                    except asyncio.TimeoutError:
                        break

            except asyncio.TimeoutError:
                # No items available, check if we should continue
                if self.shutdown_event.is_set():
                    break
                continue

            if batch:
                # log_message(f"Worker {worker_id} processing batch of {len(batch)} items", level='DEBUG')
                try:
                    async with self.semaphore:
                        await self.process_batch(batch)
                    # log_message(f"Worker {worker_id} completed batch of {len(batch)} items", level='DEBUG')
                except Exception as e:
                    log_message(f"Worker {worker_id} batch processing failed: {str(e)}", level='ERROR')
                finally:
                    # Mark all items in this batch as done
                    for _ in batch_items:
                        self.queue.task_done()

        log_message(f"Embedding worker {worker_id} stopped", level='INFO')

    async def start(self):
        """Start multiple processing workers"""
        if self.workers:
            log_message("Workers already started", level='WARNING')
            return

        # Create and start multiple workers
        for i in range(self.num_workers):
            worker_task = asyncio.create_task(self.worker(i))
            self.workers.append(worker_task)

        log_message(f"Started {self.num_workers} embedding queue workers", level='INFO')

    async def stop(self):
        """Stop all workers gracefully"""
        if not self.workers:
            return

        log_message("Stopping embedding queue workers...", level='INFO')

        # Signal shutdown
        self.shutdown_event.set()

        # Wait for workers to finish
        await asyncio.gather(*self.workers, return_exceptions=True)

        self.workers.clear()
        log_message("All embedding queue workers stopped", level='INFO')

    # --- Qdrant Deletion by ser_no (UUID) ---
    def delete_embeddings_by_ser_no(self, ser_nos, collection_name="IP_Assets"):
        """
        Delete points from Qdrant by ser_no (using generated UUIDs).
        Logs which points were deleted and which did not exist.
        Args:
            ser_nos (list): List of ser_no strings to delete.
            collection_name (str): Qdrant collection name.
        Returns:
            dict: A dictionary containing lists of 'deleted_ser_nos' and 'not_found_ser_nos'.
        """
        if not ser_nos:
            log_message("No serial numbers provided for deletion.", level='INFO')
            return {"deleted_ser_nos": [], "not_found_ser_nos": []}
 
        point_id_map = {generate_uuid(ser_no): ser_no for ser_no in ser_nos}
        point_ids_to_delete = list(point_id_map.keys())
 
        not_found_ser_nos = []
 
        try:
            # Check which points exist before attempting deletion
            existing_points = self.client.retrieve(
                collection_name=collection_name,
                ids=point_ids_to_delete,
                with_vectors=False,
                with_payload=False
            )
            existing_point_ids = {point.id for point in existing_points}
 
            # Separate existing from non-existing
            for point_id, ser_no in point_id_map.items():
                if point_id not in existing_point_ids:
                    not_found_ser_nos.append(ser_no)
                # Points that exist will be attempted for deletion
 
            if existing_point_ids:
                # Perform the delete operation only for existing points
                delete_result = self.client.delete(collection_name=collection_name, points_selector=PointIdsList(points=list(existing_point_ids)))
                if delete_result.status == UpdateStatus.COMPLETED:
                    log_message(f"Successfully deleted {len(existing_point_ids)} embeddings from Qdrant. Operation ID: {delete_result.operation_id}", level='INFO')
                else:
                    log_message(f"Qdrant delete operation failed: {delete_result}", level='WARNING')
                    # If deletion status is not 'completed', assume none were deleted for logging purposes
                    not_found_ser_nos.extend([point_id_map[pid] for pid in existing_point_ids])
 
            if not_found_ser_nos:
                log_message(f"The following {len(not_found_ser_nos)} embeddings were not found in Qdrant: {', '.join(not_found_ser_nos)}", level='INFO')
 
        except Exception as e:
            log_message(f"Error during Qdrant deletion for serial numbers {ser_nos}: {str(e)}", level='ERROR', exc_info=True)
            # If an error occurs, consider all as not found or failed to delete
            not_found_ser_nos.extend(ser_nos)
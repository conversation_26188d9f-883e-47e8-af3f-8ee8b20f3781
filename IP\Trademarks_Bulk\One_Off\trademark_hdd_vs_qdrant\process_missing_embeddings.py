#!/usr/bin/env python3
"""
Process Missing Embeddings Tool

This script reads the CSV file generated by find_missing_embeddings.py and processes
the missing embeddings for those records. It:
1. Reads the CSV file with missing embedding records
2. Generates embeddings for the image files on HDD
3. Pushes the embeddings to Qdrant

Reuses existing functions to avoid code duplication.
"""

import os
import sys
import csv
import time
import datetime
import asyncio
from logdata import log_message
from dotenv import load_dotenv

# Import database functions (reusing existing code)
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from trademark_db import get_db_connection

# Import image utility functions (reusing existing code)
from trademark_image import get_image_subdirectory

# Import embedding service (reusing existing code)
from embedding_service import EmbeddingQueue

# Import Common constants (reusing existing code)
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from Common.Constants import local_ip_folder

# Load environment variables
load_dotenv()

# Configuration
BASE_DIR = os.path.join(local_ip_folder, "Trademarks")
IMAGES_DIR = os.path.join(BASE_DIR, "USPTO_Daily", "Images")

# Default CSV file path
DEFAULT_CSV_PATH = os.path.join(os.path.dirname(__file__), 'missing_embeddings_results', 'missing_embeddings_20250724_200507.csv')


def read_missing_embeddings_csv(csv_path):
    """
    Read the CSV file with missing embedding records.
    
    Args:
        csv_path (str): Path to the CSV file
        
    Returns:
        list: List of records with missing embeddings
    """
    if not os.path.exists(csv_path):
        log_message(f"❌ CSV file not found: {csv_path}", level='ERROR')
        return []
    
    records = []
    try:
        with open(csv_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                records.append({
                    'ser_no': row['ser_no'],
                    'mark_feature_code': int(row['mark_feature_code']),
                    'image_source': row['image_source'],
                    'mark_text': row['mark_text']
                })
        
        log_message(f"✅ Successfully read {len(records)} records from CSV", level='INFO')
        return records
        
    except Exception as e:
        log_message(f"❌ Error reading CSV file: {str(e)}", level='ERROR')
        return []


def validate_image_files(records):
    """
    Validate that image files exist on HDD for all records.
    Reuses logic from existing scripts.
    
    Args:
        records (list): List of records to validate
        
    Returns:
        tuple: (valid_records, invalid_records)
    """
    valid_records = []
    invalid_records = []
    
    log_message(f"🔍 Validating image files for {len(records)} records...", level='INFO')
    
    for record in records:
        ser_no = record['ser_no']
        
        try:
            # Get image subdirectory (reusing existing function)
            image_sub_dir = get_image_subdirectory(ser_no)
            if not image_sub_dir:
                invalid_records.append((record, "Could not determine image subdirectory"))
                continue
                
            # Check if image file exists
            image_path = os.path.join(IMAGES_DIR, image_sub_dir, f"{ser_no}.webp")
            if os.path.exists(image_path):
                record['image_path'] = image_path
                valid_records.append(record)
            else:
                invalid_records.append((record, f"Image file not found: {image_path}"))
                
        except Exception as e:
            invalid_records.append((record, f"Error validating: {str(e)}"))
    
    log_message(f"✅ Validation complete: {len(valid_records)} valid, {len(invalid_records)} invalid", level='INFO')
    
    if invalid_records:
        log_message(f"⚠️ Invalid records found:", level='WARNING')
        for record, reason in invalid_records[:5]:  # Show first 5
            log_message(f"   {record['ser_no']}: {reason}", level='WARNING')
        if len(invalid_records) > 5:
            log_message(f"   ... and {len(invalid_records) - 5} more", level='WARNING')
    
    return valid_records, invalid_records


def prepare_embedding_items(valid_records):
    """
    Prepare items for embedding processing.
    
    Args:
        valid_records (list): List of validated records
        
    Returns:
        list: List of tuples (ser_no, image_path) for embedding processing
    """
    items = []
    for record in valid_records:
        items.append((record['ser_no'], record['image_path']))
    
    log_message(f"📋 Prepared {len(items)} items for embedding processing", level='INFO')
    return items


async def process_embeddings_batch(embedding_queue, items, batch_size=1000):
    """
    Process embeddings in batches using the existing EmbeddingQueue.
    
    Args:
        embedding_queue: EmbeddingQueue instance
        items (list): List of (ser_no, image_path) tuples
        batch_size (int): Size of batches for processing
        
    Returns:
        dict: Processing statistics
    """
    total_items = len(items)
    total_enqueued = 0
    total_skipped = 0
    
    log_message(f"🚀 Starting embedding processing for {total_items} items", level='INFO')
    log_message(f"⚙️ Batch size: {batch_size}", level='INFO')
    
    # Process in batches
    for i in range(0, total_items, batch_size):
        batch = items[i:i + batch_size]
        batch_num = (i // batch_size) + 1
        total_batches = (total_items + batch_size - 1) // batch_size
        
        log_message(f"\n📦 BATCH {batch_num}/{total_batches}: Processing {len(batch)} items", level='INFO')
        
        # Enqueue batch (reusing existing function)
        enqueued, skipped = await embedding_queue.batch_enqueue(batch)
        
        total_enqueued += enqueued
        total_skipped += skipped
        
        log_message(f"   📥 Enqueued: {enqueued}, ⏭️ Skipped: {skipped}", level='INFO')
        
        # Small delay between batches to prevent overwhelming the system
        if i + batch_size < total_items:
            await asyncio.sleep(1.0)
    
    # Wait for all embeddings to be processed
    if total_enqueued > 0:
        log_message(f"\n⏳ Waiting for all {total_enqueued} embeddings to be processed...", level='INFO')
        log_message(f"   This may take several minutes depending on the number of images", level='INFO')
        
        start_time = time.time()
        await embedding_queue.drain()
        processing_time = time.time() - start_time
        
        log_message(f"✅ All embeddings processed in {processing_time:.1f} seconds", level='INFO')
    else:
        log_message(f"ℹ️ No new embeddings to process (all were skipped)", level='INFO')
    
    return {
        'total_items': total_items,
        'total_enqueued': total_enqueued,
        'total_skipped': total_skipped,
        'processing_time': processing_time if total_enqueued > 0 else 0
    }


def print_processing_summary(stats, valid_records, invalid_records):
    """
    Print comprehensive processing summary.
    
    Args:
        stats (dict): Processing statistics
        valid_records (list): Valid records processed
        invalid_records (list): Invalid records skipped
    """
    print(f"\n{'='*80}")
    print(f"🧠 EMBEDDING PROCESSING SUMMARY")
    print(f"{'='*80}")
    
    print(f"\n📋 INPUT STATISTICS:")
    print(f"   Total records from CSV: {len(valid_records) + len(invalid_records):,}")
    print(f"   Valid records (with image files): {len(valid_records):,}")
    print(f"   Invalid records (missing files): {len(invalid_records):,}")
    
    print(f"\n🔄 PROCESSING STATISTICS:")
    print(f"   Items processed: {stats['total_items']:,}")
    print(f"   New embeddings created: {stats['total_enqueued']:,}")
    print(f"   Embeddings skipped (already exist): {stats['total_skipped']:,}")
    
    if stats['total_enqueued'] > 0:
        print(f"   Processing time: {stats['processing_time']:.1f} seconds")
        rate = stats['total_enqueued'] / stats['processing_time']
        print(f"   Processing rate: {rate:.1f} embeddings/second")
    
    print(f"\n🎯 COMPLETION STATUS:")
    if stats['total_enqueued'] > 0:
        print(f"   ✅ Successfully processed {stats['total_enqueued']} new embeddings")
        print(f"   📤 All embeddings have been pushed to Qdrant")
    
    if stats['total_skipped'] > 0:
        print(f"   ℹ️ {stats['total_skipped']} embeddings were already present in Qdrant")
    
    if len(invalid_records) > 0:
        print(f"   ⚠️ {len(invalid_records)} records had missing image files")
    
    if stats['total_enqueued'] == 0 and stats['total_skipped'] == 0:
        print(f"   ❌ No embeddings were processed")
    elif stats['total_enqueued'] == 0:
        print(f"   🎉 All embeddings were already present - no processing needed!")
    else:
        print(f"   🎉 Embedding processing completed successfully!")


async def main(csv_path=None):
    """
    Main function to process missing embeddings.
    
    Args:
        csv_path (str): Path to CSV file (optional, uses default if not provided)
    """
    # Use provided path or default
    if not csv_path:
        csv_path = DEFAULT_CSV_PATH
    
    log_message("🧠 Starting Missing Embeddings Processing", level='INFO')
    log_message(f"📄 CSV file: {csv_path}", level='INFO')
    print("=" * 50)
    
    # Step 1: Read CSV file
    log_message("\n📖 STEP 1: Reading CSV file", level='INFO')
    records = read_missing_embeddings_csv(csv_path)
    if not records:
        log_message("❌ No records to process", level='ERROR')
        return
    
    # Step 2: Validate image files
    log_message("\n🔍 STEP 2: Validating image files", level='INFO')
    valid_records, invalid_records = validate_image_files(records)
    if not valid_records:
        log_message("❌ No valid records with image files found", level='ERROR')
        return
    
    # Step 3: Prepare embedding items
    log_message("\n📋 STEP 3: Preparing embedding items", level='INFO')
    items = prepare_embedding_items(valid_records)
    
    # Step 4: Initialize embedding queue
    log_message("\n⚙️ STEP 4: Initializing embedding queue", level='INFO')
    embedding_queue = EmbeddingQueue(max_concurrent=3, num_workers=10)
    await embedding_queue.start()
    
    try:
        # Step 5: Process embeddings
        log_message("\n🚀 STEP 5: Processing embeddings", level='INFO')
        stats = await process_embeddings_batch(embedding_queue, items, batch_size=1000)
        
        # Step 6: Print summary
        print_processing_summary(stats, valid_records, invalid_records)
        
    finally:
        # Cleanup
        log_message("\n🧹 Cleaning up embedding queue...", level='INFO')
        await embedding_queue.stop()
    
    log_message("\n🎉 Missing embeddings processing completed!", level='INFO')


if __name__ == "__main__":
    # Configuration parameters
    # CSV_PATH = None  # Set to specific path if needed, otherwise uses default
    CSV_PATH = "IP/Trademarks_Bulk/One_Off/trademark_hdd_vs_qdrant/missing_embeddings_results/missing_embeddings_20250729_163852.csv"

    
    # Run the main function
    asyncio.run(main(csv_path=CSV_PATH))

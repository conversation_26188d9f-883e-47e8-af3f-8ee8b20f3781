<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Check API</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='api_studio/api_studio.css') }}">
    <!-- Load modular JavaScript files -->
    <script src="{{ url_for('static', filename='api_studio/utils.js') }}"></script>
    <script src="{{ url_for('static', filename='api_studio/language.js') }}"></script>
    <script src="{{ url_for('static', filename='api_studio/validation.js') }}"></script>
    <script src="{{ url_for('static', filename='api_studio/image-upload.js') }}"></script>
    <script src="{{ url_for('static', filename='api_studio/results-display.js') }}"></script>
    <script src="{{ url_for('static', filename='api_studio/check-form.js') }}"></script>
    <script src="{{ url_for('static', filename='api_studio/main.js') }}"></script>
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
</head>
<body>
    <header class="header">
        <div class="logo-title-container">
            <img src="static\images\maidalv_logo.png" alt="API Studio Logo" class="logo" style="height: 40px; margin-right: 15px;">
            <a href="https://www.maidalv.com" style="text-decoration: none; color: inherit;"><h1 class="studio-title">Maidalv API Studio (v2b6)</h1></a>
        </div>
        <nav>
            <ul>
                <li><a href="/api_studio" data-i18n="check_page">Check</a></li>
                <li><a href="/check_history" data-i18n="check_history_page">Check History</a></li>
                <li><a href="/api_studio_reverse_check" data-i18n="reverse_check_page">Reverse Check</a></li>
                <li><a href="{{ url_for('static', filename='api_demo.py') }}" download="api_demo.py" id="getCodeButton" data-i18n="get_code">Get Code</a></li>
                <li>
                    <select id="languageSelect" style="padding: 5px; border-radius: 4px;">
                        <option value="en">English</option>
                        <option value="zh">中文</option>
                    </select>
                </li>
                <!-- <li><a href="https://www.maidalv.com/" data-i18n="home">Home</a></li> -->
            </ul>
        </nav>
    </header>

    <div class="container main-content">
        <div class="left">
            <h2 data-i18n="input_form">Input Form</h2>
            <form id="checkForm">
                <div class="form-group">
                    <label for="api_key" data-i18n="api_key">API Key:</label><span class="required-field">*</span>
                    <input type="text" id="api_key" name="api_key">
                </div>
                <!-- Commented out the Check ID input as it will be auto-generated.
                <div class="form-group">
                    <label for="check_id">Check ID:</label>
                    <input type="text" id="check_id" name="check_id">
                </div>
                -->
                <div class="form-group">
                    <label for="main_product_image_url" data-i18n="main_product_image_url">Main Product Image URL:</label><span class="required-field">*</span>
                    <input type="text" id="main_product_image_url" name="main_product_image_url" placeholder="Enter image URL if not uploading">
                    <label for="main_product_image_upload" data-i18n="upload_main_image">Or Upload Main Product Image:</label>
                    <input type="file" id="main_product_image_upload" name="main_product_image_upload" accept="image/*">
                    <img id="main_product_image_preview" src="#" alt="Image Preview" style="display:none; max-width:300px; max-height:300px; margin-top:10px;">
                    <span id="cancel_preview" style="display:none; cursor:pointer; font-weight:bold; margin-top:1px;">X</span>
                    <input type="hidden" id="main_product_image_upload_active" name="main_product_image_upload_active" value="false">
                </div>
                <div class="form-group">
                    <label for="other_product_images_url" data-i18n="other_product_images">Other Product Images (Comma separated URLs):</label>
                    <input type="text" id="other_product_images_url" name="other_product_images_url" placeholder="Enter URLs separated by commas">
                    <label for="other_product_images_upload" data-i18n="upload_other_images">Or Upload Other Product Images:</label>
                    <input type="file" id="other_product_images_upload" name="other_product_images_upload" accept="image/*" multiple>
                    <div id="other_product_images_previews" style="margin-top:10px;"></div>
                    <button type="button" id="cancel_other_product_images" data-i18n="clear_images" style="display:none; cursor:pointer; font-weight:bold; margin-top:1px;">Clear selected images</button>
                    <input type="hidden" id="other_product_images_upload_active" name="other_product_images_upload_active" value="false">
                </div>
                <div class="form-group">
                    <label for="ip_images_url" data-i18n="ip_images">IP Images (Comma separated URLs):</label>
                    <input type="text" id="ip_images_url" name="ip_images_url" placeholder="Enter URLs separated by commas">
                    <label for="ip_images_upload" data-i18n="upload_ip_images">Or Upload IP Images:</label>
                    <input type="file" id="ip_images_upload" name="ip_images_upload" accept="image/*" multiple>
                    <div id="ip_images_previews" style="margin-top:10px;"></div>
                    <button type="button" id="cancel_ip_images" data-i18n="clear_images" style="display:none; cursor:pointer; font-weight:bold; margin-top:5px;">Clear selected images</button>
                    <input type="hidden" id="ip_images_upload_active" name="ip_images_upload_active" value="false">
                </div>
                <div class="form-group">
                    <label for="ip_keywords" data-i18n="ip_keywords">IP Keywords (comma separated):</label>
                    <input type="text" id="ip_keywords" name="ip_keywords">
                </div>
                <div class="form-group">
                    <label for="description" data-i18n="description">Description:</label>
                    <textarea id="description" name="description"></textarea>
                </div>
                <div class="form-group">
                    <label for="reference_text" data-i18n="reference_text">Reference Text:</label>
                    <textarea id="reference_text" name="reference_text"></textarea>
                </div>
                <div class="form-group">
                    <label for="reference_images" data-i18n="reference_images">Reference Images (comma separated URLs):</label>
                    <input type="text" id="reference_images" name="reference_images" placeholder="Enter URLs separated by commas">
                    <label for="reference_images_upload" data-i18n="upload_reference_images">Or Upload Reference Images:</label>
                    <input type="file" id="reference_images_upload" name="reference_images_upload" accept="image/*" multiple>
                    <div id="reference_images_previews" style="margin-top:10px;"></div>
                    <button type="button" id="cancel_reference_images" data-i18n="clear_images" style="display:none; cursor:pointer; font-weight:bold; margin-top:5px;">Clear selected images</button>
                    <input type="hidden" id="reference_images_upload_active" name="reference_images_upload_active" value="false">
                </div>
                <div style="margin-top: 15px;">
                    <button type="submit" id="submitButton" data-i18n="submit">Submit</button>
                </div>
                <input type="hidden" id="language" name="language" value="en">
            </form>
        </div>
        <div class="right">
            <h2 data-i18n="analysis_report">Analysis Report</h2>
            <div id="output"></div>
        </div>
    </div>

    <!-- The Modal -->
    <div id="apiCodeModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close-button" id="closeModalButton">&times;</span>
                <h2 data-i18n="python_code_title">Python API Call Code</h2>
            </div>
            <div class="modal-body">
                <pre><code class="language-python" id="pythonApiCodeSnippet"></code></pre>
            </div>
            <div class="modal-footer">
                <button type="button" id="copyCodeButton">Copy Code to Clipboard</button>
            </div>
        </div>
    </div>

    <!-- Image Preview Overlay -->
    <div class="image-overlay" id="imageOverlay">
        <span class="close-overlay">&times;</span>
        <div class="image-overlay-content">
            <img id="expandedImage" src="" alt="Expanded view">
        </div>
    </div>

</body>
</html>
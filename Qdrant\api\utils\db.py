"""
Database utilities for the API.
"""

import psycopg2
import psycopg2.extras
import pandas as pd
import json
import os
import redis
from datetime import datetime, timedelta

# Redis client for caching API keys
redis_client = redis.Redis(host=os.getenv("REDIS_HOST", "localhost"), port=int(os.getenv("REDIS_PORT", 6379)), db=0, decode_responses=True)
API_KEYS_CACHE_KEY = "api_keys_cache"
API_KEYS_CACHE_EXPIRY_SECONDS = timedelta(hours=24).total_seconds()

def get_db_connection():
    """
    Get a connection to the PostgreSQL database.
    
    Returns:
        A connection to the PostgreSQL database.
    """
    conn = psycopg2.connect(
        host=os.getenv("POSTGRES_HOST"),
        port=os.getenv("POSTGRES_PORT"),
        user=os.getenv("POSTGRES_USER"),
        password=os.getenv("POSTGRES_PASSWORD"),
        dbname=os.getenv("POSTGRES_DB")
    )
    conn.autocommit = True
    return conn


def get_ip_asset_metadata(asset_ids, ip_types):
    """
    Get metadata for IP assets from PostgreSQL.
    
    Args:
        asset_ids: A list of IP asset IDs.
        
    Returns:
        A dictionary mapping IP asset IDs to their metadata.
    """
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
    
    # Convert asset_ids to a list of UUIDs
    uuids = list(asset_ids)
    metadata = {}
    
    # Query trademarks
    if "Trademark" in ip_types:
        cursor.execute("SELECT * FROM trademarks WHERE id = ANY(%s::uuid[])",(uuids,))
        trademarks = {str(row['id']): dict(row) for row in cursor.fetchall()}
        metadata.update({id: {"type": "trademark", "data": data} for id, data in trademarks.items()})
    
    # Query patents
    if "Patent" in ip_types:
        cursor.execute("SELECT * FROM patents WHERE id = ANY(%s::uuid[])",(uuids,))
        patents = {str(row['id']): dict(row) for row in cursor.fetchall()}
        metadata.update({id: {"type": "patent", "data": data} for id, data in patents.items()})
    
    # Query copyrights
    if "Copyright" in ip_types:
        cursor.execute("SELECT * FROM copyrights WHERE id = ANY(%s::uuid[])",(uuids,))
        copyrights = {str(row['id']): dict(row) for row in cursor.fetchall()}
        metadata.update({id: {"type": "copyright", "data": data} for id, data in copyrights.items()})
    
    # Check for missing assets
    found_ids = set(metadata.keys())
    requested_ids = set(asset_ids)
    missing_ids = requested_ids - found_ids
    for missing_id in missing_ids:
        print(f"⚠️  Error: Asset ID {missing_id} not found in the database.")

    # Close connection
    cursor.close()
    conn.close()
    
    return metadata


def get_reverse_check_results_by_client_id_and_date(client_id, start_date, end_date=None):
    """
    Get all records from the reverse_check_result table for a given client_id and date/date range.
    Args:
        client_id: The client identifier to filter by.
        start_date: The start date to filter by (string, e.g., '2024-06-15').
        end_date: The end date to filter by (string, e.g., '2024-06-20'). If None, only start_date is used.
    Returns:
        A list of row dicts from reverse_check_result.
    """
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

    if end_date is None:
        # Single date query
        cursor.execute("SELECT * FROM reverse_check_result WHERE client_id = %s::text AND DATE(create_time) = %s ORDER BY create_time DESC", (client_id, start_date))
    else:
        # Date range query
        cursor.execute(
            "SELECT * FROM reverse_check_result WHERE client_id = %s::text AND DATE(create_time) BETWEEN %s AND %s ORDER BY create_time DESC",
            (client_id, start_date, end_date)
        )

    results = [dict(row) for row in cursor.fetchall()]
    cursor.close()
    conn.close()
    return results


def create_api_keys_table():
    """
    Create the check_client_api_keys table if it doesn't exist.
    """
    conn = get_db_connection()
    cursor = conn.cursor()

    create_table_sql = """
    CREATE TABLE IF NOT EXISTS check_client_api_keys (
        api_key VARCHAR(255) PRIMARY KEY,
        client_id INTEGER NOT NULL,
        client_name VARCHAR(255) NOT NULL,
        rate_limit INTEGER NOT NULL DEFAULT 1,
        daily_limit INTEGER NOT NULL DEFAULT 100,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE INDEX IF NOT EXISTS idx_client_api_keys_client_id ON check_client_api_keys(client_id);
    """

    cursor.execute(create_table_sql)
    cursor.close()
    conn.close()


def get_all_api_keys():
    """
    Retrieve all API keys from database as a pandas DataFrame.

    Returns:
        pandas.DataFrame: DataFrame with API key data
    """
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

    cursor.execute("SELECT * FROM check_client_api_keys ORDER BY client_id")
    rows = cursor.fetchall()

    cursor.close()
    conn.close()

    # Convert to DataFrame
    if rows:
        df = pd.DataFrame([dict(row) for row in rows])
        return df
    else:
        return pd.DataFrame()


def get_api_keys_as_dict():
    """
    Retrieve all API keys from database in the same format as the original JSON.

    Returns:
        dict: Dictionary with API keys as keys and config as values
    """
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

    cursor.execute("SELECT api_key, client_id, client_name, rate_limit, daily_limit FROM check_client_api_keys")
    rows = cursor.fetchall()

    cursor.close()
    conn.close()

    # Convert to dictionary format matching original JSON structure
    api_keys_dict = {}
    for row in rows:
        api_keys_dict[row['api_key']] = {
            'id': row['client_id'],
            'client_name': row['client_name'],
            'rate_limit': row['rate_limit'],
            'daily_limit': row['daily_limit']
        }

    return api_keys_dict


def get_cached_api_keys():
    """
    Get API keys with a Redis-based caching mechanism.
    Tries to fetch from Redis first. If not found, fetches from the database
    and populates the cache.

    Returns:
        dict: Dictionary with API keys as keys and config as values.
    """
    try:
        cached_keys = redis_client.get(API_KEYS_CACHE_KEY)
        if cached_keys:
            print("✅ Found API keys in Redis cache.")
            return json.loads(cached_keys)
    except redis.exceptions.ConnectionError as e:
        print(f"⚠️ Redis connection error, cannot fetch cached keys: {e}")
        # Fallback to fetching directly from DB without caching
        return get_api_keys_as_dict()

    # If cache is empty, refresh it from the database
    print("📥 API keys not found in cache, fetching from database.")
    return refresh_api_keys_cache()


def refresh_api_keys_cache():
    """
    Force refresh the API keys cache from the database and store it in Redis.

    Returns:
        dict: Fresh API keys data from the database.
    """
    print("🔄 Refreshing API keys cache from database...")
    try:
        api_keys_dict = get_api_keys_as_dict()
        
        # Serialize the dictionary to a JSON string to store in Redis
        redis_client.setex(
            API_KEYS_CACHE_KEY,
            int(API_KEYS_CACHE_EXPIRY_SECONDS),
            json.dumps(api_keys_dict)
        )
        print(f"✅ Successfully cached {len(api_keys_dict)} API keys in Redis.")
        return api_keys_dict
    except redis.exceptions.ConnectionError as e:
        print(f"🔥 Redis connection error, cannot update cache: {e}")
        # If Redis fails, we can't cache, but we can still return the keys for the current request.
        # The application will continue to work but without caching benefits until Redis is back.
        return get_api_keys_as_dict()
    except Exception as e:
        print(f"🔥 An unexpected error occurred during cache refresh: {e}")
        # Depending on desired behavior, you might want to handle this differently.
        # For now, we re-raise to make the failure visible.
        raise

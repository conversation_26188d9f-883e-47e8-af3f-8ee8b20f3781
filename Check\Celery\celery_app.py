from celery import Celery
import os
from celery.signals import worker_init
from Check.RAG.RAG_Inference import load_all_models
from Check.Data_Cache import update_dataframe_cache

# It's a good practice to use environment variables for configuration
REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')

# Check for the debug mode environment variable
# When this is set to 'True', Celery will run tasks synchronously in the same process
CELERY_ALWAYS_EAGER = os.environ.get('CELERY_ALWAYS_EAGER', 'False').lower() in ('true', '1', 't')

@worker_init.connect
def init_worker(**kwargs):
    os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
    os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-a81ed7b3-cbf2-494a-a6ff-c00b21778891"
    print("Set Langfuse environment variables for worker.")
    """
    Load models and cache in the main worker process before it forks.
    This allows child processes to share the memory via copy-on-write.
    """
    # In eager mode, workers are not initialized, so we need to load models directly
    # if we are not running in a worker context.
    if not CELERY_ALWAYS_EAGER:
        print("Initializing models for master process...")
        load_all_models()
        update_dataframe_cache()
        print("Models initialized and shared with workers.")

# Create the Celery app instance
# The first argument is the name of the current module, which is 'Check.Celery.celery_app'
# The broker is where Celery sends task messages.
# The backend is where Celery stores task states and results.
celery = Celery(
    'tasks',
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=['Check.Celery.tasks']
)

# Optional Celery configuration
celery.conf.update(
    task_serializer='json',
    accept_content=['json'],  # Ignore other content
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    # Lower the prefetch multiplier to prevent a single worker from grabbing too many tasks at once
    # This is useful for long-running tasks.
    worker_prefetch_multiplier=1,
    # Acknowledge tasks after they have been executed, not before.
    # This ensures that if a worker crashes, the task will be re-queued.
    task_acks_late=True,
    # --- ADD THIS FOR DEBUGGING ---
    task_always_eager=CELERY_ALWAYS_EAGER
)

if CELERY_ALWAYS_EAGER:
    print("✅ Celery is running in EAGER mode. Tasks will be executed locally and synchronously.")
    print("Initializing models for eager mode...")
    load_all_models()
    update_dataframe_cache()
    print("Models initialized for eager mode.")


if __name__ == '__main__':
    celery.start()
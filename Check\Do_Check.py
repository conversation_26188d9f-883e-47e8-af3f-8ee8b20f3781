import os, time, tempfile, asyncio
import concurrent.futures
from FileManagement.Tencent_COS import get_cos_client
from Check.Data_Cache import get_cached_plaintiff_df, get_cached_cases_df
from Check.Do_Check_Trademark import check_trademarks
from Check.Do_Check_Copyright import check_copyrights
from Check.Do_Check_Patent import check_patents
from langfuse import observe
import langfuse
from FileManagement.Tencent_COS import async_upload_file_with_retry
from Check.RAG.RAG_Inference import get_siglip_embeddings
from Check.Utils import insert_check_api_request_to_pg
from Check.Create_Report import create_product_url, _get_risk_assessment
from Check.Do_Check_Download import download
 
async def get_embeddings_map_in_thread(image_paths):
    """
    Runs the synchronous, CPU-bound get_siglip_embeddings
    function in a separate thread to avoid blocking the event loop.
    """
    if not image_paths:
        return {}
    # This is the key: run the blocking function in a thread pool
    embeddings = await asyncio.to_thread(get_siglip_embeddings, image_paths, "image")
    return {path: emb for path, emb in zip(image_paths, embeddings)}

async def download_and_check(check_id, client_id, client_name, api_key, product_category, main_product_image, other_product_images, client_ip_images, ip_keywords, description, reference_text, reference_images, language='zh', **kwargs):
    print(f"📋 [CHECK:{check_id}] Starting download_and_check process")
    print(f"📋 [CHECK:{check_id}] client_id: {client_id}")
    print(f"📋 [CHECK:{check_id}] product images count: main={1 if main_product_image else 0}, other={len(other_product_images)}")
    print(f"📋 [CHECK:{check_id}] ip images count: {len(client_ip_images)}")
    print(f"📋 [CHECK:{check_id}] ip_keywords count: {len(ip_keywords)}")
    print(f"📋 [CHECK:{check_id}] description length: {len(description) if description else 0}")
    print(f"📋 [CHECK:{check_id}] reference_text length: {len(reference_text) if reference_text else 0}")
    print(f"📋 [CHECK:{check_id}] reference_images count: {len(reference_images)}")
    
    # Get COS client: used to 1. download product images (in case it is not base64 or url) and 2. upload product images (in do_check, and parts in do_check_copyright)
    cos_client, cos_bucket = get_cos_client(secret_id_env="COS_MDLV_SECRET_ID",secret_key_env="COS_MDLV_SECRET_KEY",bucket="tro-1330776830")
    
    # Create temporary directory for this check
    with tempfile.TemporaryDirectory() as temp_dir:
        start_time = time.time()
        print(f"📋 [CHECK:{check_id}] Starting file downloads")
        # The download function now raises ImageDownloadError on failure.
        local_product_images, local_client_ip_images, local_reference_images = await download(cos_client, cos_bucket, temp_dir, check_id, main_product_image, other_product_images, client_ip_images, reference_images)
        download_time = time.time() - start_time

        print(f"🔨 [CHECK:{check_id}] Downloaded files in {download_time:.1f} seconds")

        # Prepare database insertion task for non-MiniApp/H5 clients
        db_insertion_future = None
        if client_name not in ["MiniApp", "H5", "MiniAppDev", "H5Dev"]:
            # Data for the new postgres table
            pg_request_data = {
                'id': int(check_id),
                'user_name': client_name,
                'api_key': api_key,
                'product_category': product_category,
                'main_product_image': create_product_url(check_id, os.path.basename(local_product_images[0])) if local_product_images else None,
                'other_product_images': [create_product_url(check_id, os.path.basename(p)) for p in local_product_images[1:]],
                'ip_images': [create_product_url(check_id, os.path.basename(p)) for p in local_client_ip_images],
                'ip_keywords': ip_keywords,
                'description': description,
                'reference_text': reference_text,
                'reference_images': [create_product_url(check_id, os.path.basename(p)) for p in local_reference_images]
            }
            
            # Start database insertion in background thread
            db_insertion_future = concurrent.futures.ThreadPoolExecutor().submit(insert_check_api_request_to_pg, pg_request_data)
            print(f"📥 Started PostgreSQL insertion for check_id: {check_id}")

        start_time = time.time()
        try:
            print(f"📋 [CHECK:{check_id}] Starting check process")
            check_kwargs = {
                'cos_client': cos_client,
                'cos_bucket': cos_bucket,
                'temp_dir': temp_dir,
                'check_id': check_id,
                'client_id': client_id,
                'local_product_images': local_product_images,
                'local_client_ip_images': local_client_ip_images,
                'local_reference_images': local_reference_images,
                'description': description,
                'ip_keywords': ip_keywords,
                'reference_text': reference_text,
                'language': language
            }
           
            results = await check(**check_kwargs)
            check_time = time.time() - start_time
            print(f"🔨 [CHECK:{check_id}] Check completed in {check_time:.1f} seconds with results count: {len(results.get('results', []))}")
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            print(f"\033[91m[CHECK:{check_id}] CRITICAL ERROR IN CHECK: {str(e)}\nTRACEBACK:\n{tb}\033[0m")
            raise
        
        # Wait for database insertion to complete if it was started
        if db_insertion_future:
            try:
                db_insertion_future.result(timeout=60)
                print(f">> Database insertion completed for check_id: {check_id}")
            except concurrent.futures.TimeoutError:
                print(f"!! Database insertion timed out for check_id: {check_id}")
            except Exception as e:
                print(f"!! Database insertion failed for check_id: {check_id}, error: {str(e)}")

        return results

# @profile
@observe(capture_input=False, capture_output=False)
async def check(cos_client, cos_bucket, temp_dir, check_id, client_id, local_product_images, local_client_ip_images, local_reference_images, description, ip_keywords, reference_text, language='zh', **kwargs):
    langfuse.get_client().update_current_span(input={
        "check_id": check_id, "client_id": client_id,
        "local_product_images": local_product_images,
        "local_client_ip_images": local_client_ip_images,
        "local_reference_images": local_reference_images,
        "description": description, "ip_keywords": ip_keywords, "reference_text": reference_text, "language": language
    })
    # Define risk levels for different languages
    risk_levels = {
        'zh': {"high": "高风险", "medium": "中风险", "low": "低风险"},
        'en': {"high": "High Risk", "medium": "Medium Risk", "low": "Low Risk"}
    }
    current_risk_levels = risk_levels.get(language, risk_levels['zh'])

    # Use cached DataFrames if available
    start_time = time.time()
    plaintiff_df = get_cached_plaintiff_df()
    cases_df = get_cached_cases_df()

    print(f"🔨 [CHECK:{check_id}] Check: got dataframes (plaintiff_df & cases_df) in {time.time() - start_time:.1f} seconds")

    # Upload the product images to COS (new)
    upload_tasks= []
    for query_image_path in local_product_images+local_client_ip_images+local_reference_images:
        print(f"COS: check uploading {query_image_path}")
        upload_tasks.append(asyncio.create_task(
            async_upload_file_with_retry(
                client=cos_client, bucket=cos_bucket,
                key=f"checks/{check_id}/query/{os.path.basename(query_image_path)}",
                file_path=query_image_path
            )
        ))    

    start_time = time.time()
    print(f"📋 [CHECK:{check_id}] Starting IP checks (trademark, copyright, patent)")

    # Start the embedding generation as a background task.
    all_original_images = local_product_images + local_client_ip_images + local_reference_images
    embedding_task = asyncio.create_task(get_embeddings_map_in_thread(all_original_images))

    tasks = [check_trademarks(cos_client, cos_bucket, temp_dir, client_id, check_id, local_product_images, local_client_ip_images, local_reference_images, description, ip_keywords, reference_text, cases_df, plaintiff_df, embedding_task=embedding_task, language=language),
             check_copyrights(cos_client, cos_bucket, temp_dir, client_id, check_id, local_product_images, local_client_ip_images, local_reference_images, cases_df, plaintiff_df, embedding_task=embedding_task, language=language),
             check_patents(cos_client, cos_bucket, temp_dir, client_id, check_id, local_product_images, local_client_ip_images, local_reference_images, description, ip_keywords, reference_text, cases_df, plaintiff_df, embedding_task=embedding_task, language=language)
             ]
    all_ip_results = await asyncio.gather(*tasks)
    ip_check_time = time.time() - start_time
    
    results = [item for sublist in all_ip_results if sublist is not None for item in sublist]
    print(f"🔨 [CHECK:{check_id}] All tasks (trademark, copyright, patent) completed in {ip_check_time:.1f} seconds, with total raw results: {len(results)}")
    
    start_time = time.time()
    # Filter out results that don't have a risk score
    results_with_scores = [r for r in results if "risk_score" in r]
    
    # Determine overall risk level based on the highest score
    if not results_with_scores:
        overall_risk_level = current_risk_levels["low"]
    else:
        highest_score_result = max(results_with_scores, key=lambda r: r["risk_score"])
        highest_score = highest_score_result["risk_score"]
        is_tro = "plaintiff_id" in highest_score_result and highest_score_result["plaintiff_id"]
        
        # We need to derive the original score before the TRO adjustment to get the correct qualitative level
        original_score = highest_score - 1 if is_tro and highest_score > 0 else highest_score
        
        overall_risk_level, _ = _get_risk_assessment(original_score, is_tro, highest_score_result["ip_type"], language)

    seen_keys = set()
    unique_results = []
    for result in results:
        # Determine key type and validate
        if "ip_local_paths" in result and len(result["ip_local_paths"]) > 0: # Trademark text now have an empty list for ip_local_paths
            current_key = (result["ip_type"], result["ip_local_paths"][0])
        else:
            # print(f"ip_owner for result: {result['ip_owner']} of type: {result['type']}")
            current_key = (result["ip_type"], result["ip_owner"])

        # Common processing for both cases
        if current_key not in seen_keys:
            seen_keys.add(current_key)
            
            if "plaintiff_id" in result and result["plaintiff_id"]:
                # This is already done in qdrant_search, but not for trademark text. In the future maybe we only keep it here.
                plaintiff_id = int(float(result["plaintiff_id"]))
                plaintiff_info = plaintiff_df[plaintiff_df['id'] == plaintiff_id]
                if not plaintiff_info.empty:
                    result['plaintiff_name'] = plaintiff_info.iloc[0]['plaintiff_name']
                
                case_info = cases_df[cases_df["plaintiff_id"] == plaintiff_id]
                result["number_of_cases"] = case_info.shape[0]
                if not case_info.empty:
                    last_case = case_info.sort_values("date_filed", ascending=False).iloc[0]
                    result["last_case_docket"] = last_case['docket']
                    result["last_case_date_filed"] = str(last_case['date_filed'])
                    
            unique_results.append(result)

    print(f"📋 [CHECK:{check_id}] Unique results: {len(unique_results)}")

    # Sort the results by risk score (descending) and then by type.
    def risk_and_type_sort_key(result):
        # Default to -1 if risk_score is not present
        score = result.get("risk_score", -1)
        return (-score, result["ip_type"])

    unique_results.sort(key=risk_and_type_sort_key)

    # Clean up results before returning by removing temporary keys
    keys_to_pop = ["product_local_path", "internal_type", "ip_local_paths", "similarity"]
    for result in unique_results:
        for key in keys_to_pop:
            result.pop(key, None)

    print(f'🔨 [CHECK:{check_id}] Check Analysis Completed with breakdown by risk level: {sum(1 for r in unique_results if r["risk_level"] == current_risk_levels["high"])} high, {sum(1 for r in unique_results if r["risk_level"] == current_risk_levels["medium"])} medium, {sum(1 for r in unique_results if r["risk_level"] == current_risk_levels["low"])} low')

    await asyncio.gather(*upload_tasks)  # the upload of the original query images to the COS bucket
    print(f'📋 [CHECK:{check_id}] Uploaded {len(upload_tasks)} query images to COS')

    final_results = {
        "check_id": check_id,
        "status": "success",
        "risk_level": overall_risk_level,
        "results": unique_results
    }
    langfuse.get_client().update_current_span(output=final_results)
    return final_results
#!/usr/bin/env python3
"""
Migration script to add and initialize the 'filenames' column in the 'copyrights' table.
"""

import os
import sys

sys.path.append(os.getcwd())

from IP.Patents_Bulk.patent_db_grant import get_db_connection

def migrate_copyrights_table():
    """
    Adds the 'filenames' column to the 'copyrights' table and initializes it.
    """
    db_conn = None
    cursor = None
    try:
        db_conn = get_db_connection()
        cursor = db_conn.cursor()

        # Add the 'filenames' column
        cursor.execute("ALTER TABLE copyrights ADD COLUMN filenames _text;")
        print("✅ 'filenames' column added to 'copyrights' table.")

        # Initialize the 'filenames' column
        cursor.execute("""
            UPDATE copyrights
            SET filenames = ARRAY[registration_number || '_' || method || '.webp']
            WHERE method IS NOT NULL;
        """)
        print("✅ 'filenames' column initialized successfully.")

        db_conn.commit()

    except Exception as e:
        if db_conn:
            db_conn.rollback()
        print(f"❌ Error during migration: {e}")
    finally:
        if cursor:
            cursor.close()
        if db_conn:
            db_conn.close()

if __name__ == "__main__":
    migrate_copyrights_table()
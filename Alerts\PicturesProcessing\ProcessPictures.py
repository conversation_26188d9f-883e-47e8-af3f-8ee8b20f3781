import os, sys, imagehash, shutil, re, json, fitz, pickle, asyncio, time, multiprocessing, traceback
from typing import Optional, Tuple, Dict, List, Any # Added Optional, Tuple, Dict, List, Any
sys.path.append(os.getcwd())
import numpy as np
from PIL import Image
import pandas as pd
from logdata import log_message
from Common.Constants import local_case_folder, local_plaintiff_folder, sanitize_name
from Alerts.IPTrackingManager import IPTrackingManager # Added IPTrackingManager import
start_time = time.time()

from Alerts.PicturesProcessing.Trademarks_Exhibits import process_trademark_exhibit
from Alerts.PicturesProcessing.Trademarks_RegNo import process_trademark_regno
from Alerts.PicturesProcessing.Trademarks_ByName import get_trademark_data_by_name

from Alerts.PicturesProcessing.Copyrights_Exhibits import process_copyright_exhibit
from Alerts.PicturesProcessing.Copyrights_USCO import get_copyright_data_by_name

from Alerts.PicturesProcessing.Patents import process_patent_exhibit, get_and_process_patents_from_uspto_using_regnos, get_and_process_patents_from_uspto_using_plaintiff_name
from IP.Patent import clean_reg_no

from IP.Trademarks.USPTO_TSDR_API import format_reg_number
if multiprocessing.current_process().name == 'MainProcess':
    print(f"   ProcessPictures - Trademark after {time.time()-start_time:.2f} seconds") # 27 seconds!

start_time = time.time()

if multiprocessing.current_process().name == 'MainProcess':
    print(f"   ProcessPictures - TradeCopyright after {time.time()-start_time:.2f} seconds") # 2.5 sec


from Scraper.ChineseWebsites import scrape_case_data

from Alerts.PicturesProcessing.ProcessPicturesShared import keywords
from DatabaseManagement.ImportExport import get_table_from_GZ
from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor
from langfuse import observe
import langfuse
from Alerts.Plaintiff import generate_search_terms_for_ip # Changed from remove_acronyms

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗


def delete_directory(directory):
    if os.path.exists(directory):
        shutil.rmtree(directory)


# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# +++ NEW FUNCTIONS FOR ITERATIVE PROCESSING ++++++++++++++++++++++++++++++++++
# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++


@observe(capture_input=False, capture_output=False)
async def process_attachement_pdfs(pdf_paths: List[str], df: pd.DataFrame, index: Any, case_directory: str, step_nb: str, copyright_template1_hash: Any, copyright_template1_size: Any, certainty: str, ip_manager: IPTrackingManager) -> Tuple[Dict[str, Any], bool]:
    """
    Processes a list of downloaded PDF files for a specific case step to find IP exhibits.
    Updates the ip_manager and returns OCR data.

    Args:
        pdf_paths (list): List of absolute paths to PDF files for this step.
        df (pd.DataFrame): The main DataFrame (modified in place).
        plaintiff_df (pd.DataFrame): Plaintiff DataFrame.
        index: The index of the current case row in df.
        case_directory (str): Base directory for the case (e.g., /path/to/YYYY-MM-DD - docket).
        step_nb (str): The step number being processed.
        copyright_template1_hash: Precomputed hash for copyright template.
        copyright_template1_size: Precomputed size for copyright template.
        ip_manager (IPTrackingManager): Manager tracking the IP finding status.

    Returns:
        Tuple[Dict[str, Any], bool]: A tuple containing:
            - Dict[str, Any]: OCR data collected for the processed PDFs {pdf_path: [page_ocr_data, pages_text_json, page_images]}.
            - bool: True if a rubbish report was detected, False otherwise.
    """
    need_llm_file_paths = []
    log_message(f"        Processing {len(pdf_paths)} downloaded PDFs for step {step_nb}...")
    pdf_ocr_data_for_step = {}
    is_rubbish_report = False # Applies to the last PDF of pdf_paths
    case_images_directory = os.path.join(case_directory, 'images') # Define image dir path
    os.makedirs(case_images_directory, exist_ok=True) # Ensure image dir exists

    # Ensure 'images' and 'images_status' are initialized for the index
    # This should ideally happen once before the iterative loop starts
    if pd.isna(df.at[index, 'images']) or not isinstance(df.at[index, 'images'], dict):
        df.at[index, 'images'] = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
    if pd.isna(df.at[index, 'images_status']) or not isinstance(df.at[index, 'images_status'], dict):
        initialize_case_image_status(df, index) # Initialize status if not already done


    for pdf_file_path in pdf_paths:
        need_llm = True
        pdf_file = os.path.basename(pdf_file_path)
        step_directory_path = os.path.dirname(pdf_file_path) # Get the step dir from the path
        pdf_file_no_ext = os.path.splitext(pdf_file)[0]
        image_subfolder_path = os.path.join(step_directory_path, pdf_file_no_ext) # Subfolder for images from this PDF
        location_id = f"{step_nb}-{pdf_file_no_ext}" # Define location ID for IP Manager

        log_message(f"           - Processing PDF: {pdf_file}")

        try:
            pdf_document = fitz.open(pdf_file_path)
            if pdf_document.page_count == 0:
                log_message(f"           ! Skipping corrupted PDF (0 pages): {pdf_file}")
                continue

            os.makedirs(image_subfolder_path, exist_ok=True) # Ensure subfolder for extracted images exists

            # --- Rubbish Report Check ---
            try:
                log_message(f"           Performing preliminary rubbish check...")
                pdf_document_check = fitz.open(pdf_file_path)
                num_pages_to_check = min(4, pdf_document_check.page_count)
                if num_pages_to_check > 1:
                    # Use existing OCRProcessor, but only for first few pages
                    rubbish_text, pages_text_json, page_ocr_data, page_images = OCRProcessor.process_pdf(pdf_document_check, range(num_pages_to_check))
                    
                    # Do not include "civil procedure" as it is too generic and can be in a complaint
                    rubbish_keywords = [
                        "Security Imperative", "illicit trade of goods", "heavy recruitment of chinese",
                        "counterfeit and pirated goods", "office of strategy", "counterfeiting in the age of the internet",
                        "accreditation", "department of commerce", "white paper", "chamber of commerce",
                        "office of trade", "border protection", "hague convention",
                        "convention on the service", "seizure statistics", "notorious markets", " inta ",
                        "traffic report", "silk road", "state of the", "briefing papers"
                    ]
                    if any(keyword.lower() in rubbish_text.lower() for keyword in rubbish_keywords):
                        log_message(f"           🚫 Rubbish report detected in {pdf_file} based on first {num_pages_to_check} pages.")
                        is_rubbish_report = True
                        # Clean up image subfolder if it was created
                        image_subfolder_path_check = os.path.join(os.path.dirname(pdf_file_path), os.path.splitext(os.path.basename(pdf_file_path))[0])
                        if os.path.exists(image_subfolder_path_check):
                            shutil.rmtree(image_subfolder_path_check)
                        pdf_document_check.close()
                        break # Stop processing this step's PDFs

                        
                    # --- Check for Multi-Case Document ---
                    if 2 in pages_text_json:
                        top_of_page_2 = pages_text_json[2][:450]
                        case_numbers = re.findall(r"-cv-(\d+)", top_of_page_2)
                        if len(set(case_numbers)) > 1 or (len(set(case_numbers)) == 1 and case_numbers[0] != df.at[index, 'docket'].split("-cv-")[-1]):
                            log_message(f"           🔥Multi-case document detected. Skipping IP search for this PDF.")
                            shutil.rmtree(image_subfolder_path)
                            if pdf_file_path in pdf_ocr_data_for_step:
                                del pdf_ocr_data_for_step[pdf_file_path]
                            continue
                    
                    log_message(f"           Preliminary check passed for {pdf_file}.")
                else:
                    log_message(f"           Preliminary check not applicable for {pdf_file} (only 1 p.a.g.e.).")
                    pdf_document_check.close()
                    continue
                pdf_document_check.close()
            except Exception as rubbish_err:
                log_message(f"      ⚠️ Error during rubbish check for {pdf_file}: {rubbish_err}. Proceeding with caution.", level='WARNING')
                if 'pdf_document_check' in locals() and pdf_document_check and not pdf_document_check.is_closed:
                    pdf_document_check.close()
            # --- End Rubbish Report Check ---

            # --- Original Processing Starts Here ---
            try: # Re-open or use existing handle if check didn't open/close
                # Re-opening is safer in case the check had issues
                pdf_document = fitz.open(pdf_file_path)
                os.makedirs(image_subfolder_path, exist_ok=True) # Ensure subfolder for extracted images exists

                # --- Perform OCR ---
                log_message(f"           Performing OCR...")
                full_text, pages_text_json, page_ocr_data, page_images = OCRProcessor.process_pdf(pdf_document, range(pdf_document.page_count))
                pdf_ocr_data_for_step[pdf_file_path] = [page_ocr_data, pages_text_json, page_images]
                log_message(f"           OCR complete.")
            except Exception as e:
                log_message(f"           ❌ Error in OCR processing of PDF {pdf_file}: {e}\n{traceback.format_exc()}")
                if 'pdf_document' in locals() and pdf_document and not pdf_document.is_closed:
                    pdf_document.close()
                continue

            # --- Process for IP Exhibits ---
            is_trademark_exhibit = False
            is_patent_exhibit = False
            is_copyright_exhibit = False
            has_copyright_pictures_in_pdf = False

            # Trademark Check
            try: 
                if "principal register" in full_text.lower():
                    log_message(f"           💡 Trademark keyword found. Processing exhibit...")
                    # Update call to handle tuple return
                    is_trademark_exhibit, exhibit_reg_nos = await process_trademark_exhibit(df, index, case_images_directory, step_directory_path, pdf_document, pdf_file, pages_text_json, page_ocr_data, page_images, ip_manager)
                    if is_trademark_exhibit:
                        log_message(f"      ✅ Found Trademark Exhibit in {pdf_file} (Reg/Ser Nos: {exhibit_reg_nos})")
                        ip_manager.record_finding('trademark', location_id, exhibit_reg_nos)
                        need_llm = False

                # Patent Check
                if any(all(keyword in full_text.lower() for keyword in keywordset) for keywordset in keywords["Patent"]):
                    log_message(f"           💡 Patent keyword found. Processing exhibit...")
                    # Update call to handle tuple return (bool, list[str])
                    is_patent_exhibit, exhibit_reg_nos = await process_patent_exhibit(df, index, case_directory, pdf_file_path)
                    if is_patent_exhibit or exhibit_reg_nos: # Record finding if exhibit processed or reg nos found even if processing failed
                        log_message(f"           {'✅ Found Patent Exhibit (processed successfully)' if is_patent_exhibit else '❕ Found Patent Exhibit (processing failed)'} in {pdf_file} (Reg Nos: {exhibit_reg_nos})")
                        ip_manager.record_finding('patent', location_id, exhibit_reg_nos)
                        need_llm = False

                # Copyright Check
                keyword_required = 3
                if any(sum(keyword in full_text.lower() for keyword in keyword_set) >= keyword_required for keyword_set in keywords["Copyright"]):
                    log_message(f"      💡 Copyright keyword found. Processing exhibit...")
                    
                    exhibit_reg_nos_identified = await process_copyright_exhibit(
                        df, index, copyright_template1_hash, copyright_template1_size,
                        case_images_directory, step_directory_path, pdf_document, pdf_file,
                        pages_text_json, certainty, ip_manager, location_id # Pass ip_manager and location_id
                    )
                    if len(exhibit_reg_nos_identified) > 0:
                        need_llm = False

            except Exception as e:
                log_message(f"      ❌ Error processing exhibit in {pdf_file}: {e}\n{traceback.format_exc()}")
                
            pdf_document.close() # Close the PDF document

            if need_llm:
                need_llm_file_paths.append(pdf_file_path)
        
        except Exception as e:
            log_message(f"      ❌ Error processing PDF {pdf_file}: {e}\n{traceback.format_exc()}")
            if 'pdf_document' in locals() and pdf_document and not pdf_document.is_closed:
                pdf_document.close()

    # After processing all PDFs in the list, return collected OCR data.
    # Sufficiency check is now handled by the calling function using ip_manager.
    log_message(f"            Finished processing PDFs for step {step_nb}. Rubbish detected: {is_rubbish_report}")

    langfuse.get_client().update_current_span(
        input={
            "NumPDFs": len(pdf_paths),
            "CaseIndex": index,
            "StepNB": step_nb,
            "Certainty": certainty,
            "IPManagerRelevantGoalsBefore": {
                "TM": ip_manager.is_goal_relevant('trademark'),
                "CR": ip_manager.is_goal_relevant('copyright'),
                "PT": ip_manager.is_goal_relevant('patent')
            }
        },
        output={
            "RubbishReportDetected": is_rubbish_report,
            "NumPDFsProcessedForOCR": len(pdf_ocr_data_for_step),
            "NeedLLMFilePathsCount": len(need_llm_file_paths),
            "IPManagerGoalsAfter": {
                "TM": ip_manager.is_goal_met('trademark'),
                "CR": ip_manager.is_goal_met('copyright'),
                "PT": ip_manager.is_goal_met('patent')
            }
        }
    )
    return pdf_ocr_data_for_step, is_rubbish_report, need_llm_file_paths # is_rubbish_report and need_llm only work when pdf_paths contains a single PDF


@observe(capture_input=False, capture_output=False)
async def ChineseWebsites(df: pd.DataFrame, plaintiff_df: pd.DataFrame, index: Any, case_images_directory: str, ip_manager: IPTrackingManager) -> None:
    """
    Searches Chinese websites for IP information.
    """
    row = df.loc[index]
    docket_number = row["docket"]
    date_filed = row["date_filed"]
    plaintiffs_name_series = plaintiff_df[plaintiff_df["id"] == row["plaintiff_id"]]["plaintiff_name"] # Use .get for series
    plaintiff_name = None
    if not plaintiffs_name_series.empty:
        plaintiff_name = plaintiffs_name_series.values[0]
    
    if not plaintiff_name:
        log_message(f"  ❌ Searching Chinese Websites for {docket_number}: No plaintiff name found in plaintiff_df for plaintiff_id {row['plaintiff_id']}. Skipping...", level='INFO')
        return


    log_message(f"  ⛏ Searching Chinese Websites for {docket_number}...", level='INFO')
    # Pass case_images_directory to scrape_case_data
    results = await scrape_case_data(df, date_filed, docket_number, plaintiff_name, ["patent", "trademark", "copyright"], case_images_directory, ip_manager)

    # --- Process Copyrights from Chinese Websites ---
    copyright_processed_images = results.get("copyright_processed_images", [])
    if copyright_processed_images:
        processed_copyright_count_cn = 0
        all_usco_reg_nos_from_cn = set()
        log_message(f"  Processing {len(copyright_processed_images)} copyright images from Chinese Websites results...")

        for item in copyright_processed_images:
            final_image_path = item.get("final_image_path")
            usco_reg_no = item.get("matched_usco_reg_no")
            source_site = item.get("source_site", "UnknownCN")

            if not final_image_path or not os.path.exists(final_image_path):
                log_message(f"    Skipping item due to missing or non-existent final_image_path: {final_image_path}", level="WARNING")
                continue

            image_key_filename = os.path.basename(final_image_path)


            all_usco_reg_nos_from_cn.add(usco_reg_no)
            found_existing_for_reg_no = False

            for existing_key, details in df.at[index, 'images']['copyrights'].items():
                existing_reg_nos_list = details.get('reg_no', [])
                if not isinstance(existing_reg_nos_list, list): existing_reg_nos_list = [existing_reg_nos_list]

                if usco_reg_no and usco_reg_no in existing_reg_nos_list:
                    found_existing_for_reg_no = True
                    original_certificate_basename = details.get('full_filename', [""])[0]
                    if not original_certificate_basename: # Should not happen if entry is valid
                        original_certificate_basename = existing_key # Fallback
                    
                    log_message(f"    Reg_no {usco_reg_no} found in existing entry '{existing_key}'. Replacing artwork with '{image_key_filename}', certificate: '{original_certificate_basename}'.", level="DEBUG")
                    
                    if existing_key != image_key_filename: # Avoid pop and add if key is the same
                        df.at[index, 'images']['copyrights'].pop(existing_key)
                    
                    df.at[index, 'images']['copyrights'][image_key_filename] = {'full_filename': [original_certificate_basename], 'reg_no': [usco_reg_no]}
                    processed_copyright_count_cn += 1
                    break 

            if not found_existing_for_reg_no:
                log_message(f"    Adding new copyright entry for {usco_reg_no} with image '{image_key_filename}' as artwork and certificate.", level="DEBUG")
                df.at[index, 'images']['copyrights'][image_key_filename] = {'full_filename': [image_key_filename], 'reg_no': [usco_reg_no] }
                processed_copyright_count_cn += 1

            if usco_reg_no:
                location_id = f"cn_website-{source_site}-{usco_reg_no}-{image_key_filename}"
                ip_manager.record_finding('copyright', location_id, [usco_reg_no])

            if source_site not in df.at[index, 'images_status']['copyright_status']['cn_website']['sources']:
                df.at[index, 'images_status']['copyright_status']['cn_website']['sources'][source_site] = 0
            df.at[index, 'images_status']['copyright_status']['cn_website']['sources'][source_site] += 1

        # Update images_status totals for cn_website copyrights
        existing_cn_reg_nos = set(df.at[index, 'images_status']['copyright_status']['cn_website'].get('cn_reg_nos', []))
        existing_cn_reg_nos.update(all_usco_reg_nos_from_cn)
        # Filter out None values before sorting
        df.at[index, 'images_status']['copyright_status']['cn_website']['cn_reg_nos'] = sorted(list(filter(None, existing_cn_reg_nos)))
        df.at[index, 'images_status']['copyright_status']['cn_website']['count'] += processed_copyright_count_cn

        if processed_copyright_count_cn > 0:
            log_message(f"  ✅ Processed {processed_copyright_count_cn} copyright images from Chinese Websites.")
        else:
            log_message(f"  No new copyright images processed from Chinese Websites results.")
    # --- End Copyrights Processing ---

    # Process trademark numbers
    if len(results.get("trademarks",[])) > 0:
        reg_nos = []
        ser_nos = []
        for reg_no in results.get("trademarks", []):
            if isinstance(reg_no, (str, int)) and not str(reg_no).startswith("3736-0624"): # Filter out known invalid patterns
                reg_no_str = str(reg_no).strip()

                if len(reg_no_str) == 8:
                    ser_nos.append(str(reg_no_str))
                elif len(reg_no_str) == 7:
                    formatted = format_reg_number(reg_no_str)
                    if formatted:  # Only add if format_reg_number returned a valid, non-None string
                        reg_nos.append(str(formatted)) # Ensure it's a string for set operations
                

        valid_reg_nos = list(set(reg_nos))
        valid_ser_nos = list(set(ser_nos))
        log_message(f"  Found {len(valid_reg_nos)} valid trademarks registration numbers and {len(valid_ser_nos)} valid trademarks serial numbers on Chinese Websites...")

        if 'byregno' not in df.at[index, 'images_status']['trademark_status']: df.at[index, 'images_status']['trademark_status']['byregno'] = {}
        if 'cn_reg_nos' not in df.at[index, 'images_status']['trademark_status']['byregno']: df.at[index, 'images_status']['trademark_status']['byregno']['cn_reg_nos'] = []
        current_cn_nos = df.at[index, 'images_status']['trademark_status']['byregno']['cn_reg_nos']
        # Filter None from valid_tm_nos before adding to current_cn_nos

        filtered_valid_reg_nos = [r for r in valid_reg_nos if r is not None]
        filtered_valid_ser_nos = [r for r in valid_ser_nos if r is not None]
        
        df.at[index, 'images_status']['trademark_status']['byregno']['cn_reg_nos'] = list(set(current_cn_nos + filtered_valid_reg_nos + filtered_valid_ser_nos))
        reg_nos_to_process = filtered_valid_reg_nos + filtered_valid_ser_nos # Now it contain both reg numbers as well as serial numbers
        reg_nos_to_process = [reg_no for reg_no in reg_nos_to_process if reg_no not in ip_manager._state["trademark"]["found_reg_nos"]]
        
        if reg_nos_to_process:
            log_message(f"    Processing {len(reg_nos_to_process)} new trademark reg numbers from Chinese Website...")
            await process_trademark_regno(df, index, case_images_directory, reg_nos_to_process, ip_manager=ip_manager, source="ChineseWebsite/Scraping")
            # It handle both reg numbers as well as serial numbers too
        else:
            log_message(f"    Chinese Website found trademark reg numbers, but they were already processed or invalid.")

    # Process patent numbers
    if len(results.get("patents",[])) > 0:
        valid_pt_nos = [clean_reg_no(reg_no) for reg_no in results["patents"] if reg_no]
        valid_pt_nos = list(set([reg_no for reg_no in valid_pt_nos if reg_no]))
        
        log_message(f"  Found {len(valid_pt_nos)} patents on Chinese Websites...")
        if 'byregno' not in df.at[index, 'images_status']['patent_status']: df.at[index, 'images_status']['patent_status']['byregno'] = {}
        if 'cn_reg_nos' not in df.at[index, 'images_status']['patent_status']['byregno']: df.at[index, 'images_status']['patent_status']['byregno']['cn_reg_nos'] = []
        current_cn_nos = df.at[index, 'images_status']['patent_status']['byregno']['cn_reg_nos']
        df.at[index, 'images_status']['patent_status']['byregno']['cn_reg_nos'] = list(set(current_cn_nos + valid_pt_nos))

        already_found_patents = [value_json.get("patent_number") for value_json in df.at[index, 'images'].get("patents", {}).values() if value_json.get("patent_number")]
        reg_nos_to_process = [reg_no for reg_no in valid_pt_nos if reg_no not in already_found_patents]

        if reg_nos_to_process:
            log_message(f"    Processing {len(reg_nos_to_process)} new patent reg numbers from Chinese Website...")
            await get_and_process_patents_from_uspto_using_regnos(df, index, os.path.dirname(case_images_directory), reg_nos_to_process, ip_manager=ip_manager)
        else:
            log_message(f"    Chinese Website found patent reg numbers, but they were already processed or invalid.")


@observe(capture_input=False, capture_output=False)
async def SearchByPlaintiffName(df: pd.DataFrame, plaintiff_df: pd.DataFrame, index: Any, case_directory: str, case_images_directory: str, ip_manager: IPTrackingManager) -> None:
    """
    Searches for IP by plaintiff name if goals are still unmet.
    """
    row = df.loc[index]
    main_plaintiff_id = row.get('plaintiff_id')
    nos_desc = row.get('nos_description', '').lower()

    # Get main plaintiff name
    plaintiff_id = df.at[index, "plaintiff_id"]
    main_plaintiff_name_row = plaintiff_df[plaintiff_df["id"] == plaintiff_id]

    if main_plaintiff_name_row.empty:
        log_message(f"        - Main Plaintiff ID {plaintiff_id} not found in plaintiff_df. Cannot effectively search by name.", level='WARNING')
        return # Early exit if main plaintiff name cannot be determined
    main_plaintiff_name = main_plaintiff_name_row["plaintiff_name"].values[0]
    
    # Note: The generate_search_terms_for_ip helper is called within each specific IP type search function now.
    log_message(f"  ⛏ Searching by plaintiff name for case {df.at[index, 'docket']} (Plaintiff: {main_plaintiff_name})...", level='INFO')

    # Copyrights by Name (USCO + Google)
    # if not ip_manager.is_goal_met('copyright') and "copyright" in nos_desc:
    #     await get_copyright_data_by_name(df, index, case_images_directory, plaintiff_df, ip_manager)

    # Patents by Name (USPTO)
    if not ip_manager.is_goal_met('patent') and "patent" in nos_desc:
        log_message(f"    Patent goal unmet. Searching USPTO for patents by plaintiff name...", level='INFO')
        await get_and_process_patents_from_uspto_using_plaintiff_name(df, index, case_directory, plaintiff_df, ip_manager=ip_manager)

    # Trademarks by Name (USPTO)
    if not ip_manager.is_goal_met('trademark') and "trademark" in nos_desc:
        log_message(f"    Trademark goal unmet. Searching USPTO for trademarks by plaintiff name...", level='INFO')
        await get_trademark_data_by_name(df, index, case_images_directory, plaintiff_df, ip_manager=ip_manager)


@observe(capture_input=False, capture_output=False)
def create_resized_images(df: pd.DataFrame, index: Any, case_directory: str, case_images_directory: str) -> pd.DataFrame:
    """
    Creates resized versions of images found in the case_images_directory.
    Updates the DataFrame by removing entries for images that failed to resize.

    Args:
        df (pd.DataFrame): The main DataFrame.
        index: The index of the current case row in df.
        case_directory (str): Base directory for the case.
        case_images_directory (str): Directory containing the original images.

    Returns:
        pd.DataFrame: The modified DataFrame.
    """
    log_message(f"    Resizing images in {case_images_directory}...")
    image_files = [
        f for f in os.listdir(case_images_directory)
        if os.path.isfile(os.path.join(case_images_directory, f)) and f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp', '.jpx'))
    ]
    if image_files:
        log_message(f"      Found {len(image_files)} images to resize.")
        args = [(case_images_directory, filename, case_directory) for filename in image_files]
        try:
            pool = OCRProcessor.get_pool()
            if pool:
                results = pool.starmap(OCRProcessor.create_resized_image, args)
                failed_resizes = []
                for image_filename, success_resize in zip(image_files, results):
                    if not success_resize:
                        failed_resizes.append(image_filename)
                        # Safely remove from dicts
                        df.at[index, 'images'].get('trademarks', {}).pop(image_filename, None)
                        df.at[index, 'images'].get('patents', {}).pop(image_filename, None)
                        df.at[index, 'images'].get('copyrights', {}).pop(image_filename, None)
                if failed_resizes:
                    log_message(f"\033[91m      ! Failed to resize {len(failed_resizes)} images: {', '.join(failed_resizes)}\033[0m")
                else:
                    log_message(f"      Successfully resized {len(image_files) - len(failed_resizes)} images.")
            else:
                log_message("      Error: OCRProcessor pool not available for resizing images.")
        except Exception as resize_err:
            log_message(f"      Error during image resizing: {resize_err}")
    else:
        log_message("      No images found to resize.")

    langfuse.get_client().update_current_span(
        input={
            "CaseIndex": index,
            "CaseImagesDirectory": os.path.basename(case_images_directory),
            "NumImageFiles": len(image_files) if 'image_files' in locals() else 0
        },
        output={
            "SuccessfullyResized": len(image_files) - len(failed_resizes) if 'image_files' in locals() and 'failed_resizes' in locals() else 'N/A',
            "FailedResizes": len(failed_resizes) if 'failed_resizes' in locals() else 'N/A'
        }
    )
    return df


@observe(capture_input=False, capture_output=False)
async def process_trademark_registration_numbers(case_images_directory: str, df: pd.DataFrame, index: Any, tm_nos: List[str], ip_manager: IPTrackingManager):

    # Process trademark registration numbers
    if tm_nos:
        # Ensure list exists before extending
        if 'byregno' not in df.at[index, 'images_status']['trademark_status']: df.at[index, 'images_status']['trademark_status']['byregno'] = {}
        if 'ai_reg_nos' not in df.at[index, 'images_status']['trademark_status']['byregno']: df.at[index, 'images_status']['trademark_status']['byregno']['ai_reg_nos'] = []
        current_ai_nos = df.at[index, 'images_status']['trademark_status']['byregno']['ai_reg_nos']
        df.at[index, 'images_status']['trademark_status']['byregno']['ai_reg_nos'] = list(set(current_ai_nos + tm_nos)) # Append unique

        valid_tm_nos = [str(reg_no) for reg_no in tm_nos if isinstance(reg_no, (str, int)) and not str(reg_no).startswith("3736-0624")]
        reg_nos_to_process = [reg_no for reg_no in valid_tm_nos if reg_no not in ip_manager._state["trademark"]["found_reg_nos"]]

        if reg_nos_to_process:
            log_message(f"    🔑 Processing {len(reg_nos_to_process)} new trademark reg numbers...") # Removed 'from LLM'
            # Pass ip_manager to process_trademark_regno
            await process_trademark_regno(df, index, case_images_directory, reg_nos_to_process, ip_manager=ip_manager, source="LLM/MainDoc/Unresolved") # Pass ip_manager
        else:
            log_message(f"    Found trademark reg numbers, but they were already processed or invalid.") # Removed 'LLM'

    langfuse.get_client().update_current_span(
        input={
            "CaseIndex": index,
            "NumTMNosToProcess": len(tm_nos),
            "Source": "LLM/MainDoc/Unresolved" # Assuming this is a fixed string based on the f-string
        },
        output={
            "TMNosProcessedForUSPTO": len(reg_nos_to_process) if 'reg_nos_to_process' in locals() else 0,
            "TrademarkGoalMetAfter": ip_manager.is_goal_met('trademark')
        }
    )

@observe(capture_input=False, capture_output=False)
async def process_patent_registration_numbers(case_images_directory: str, df: pd.DataFrame, index: Any, pt_nos: List[str], ip_manager: IPTrackingManager):

    # Process patent registration numbers
    if pt_nos:
        if 'byregno' not in df.at[index, 'images_status']['patent_status']: df.at[index, 'images_status']['patent_status']['byregno'] = {}
        if 'ai_reg_nos' not in df.at[index, 'images_status']['patent_status']['byregno']: df.at[index, 'images_status']['patent_status']['byregno']['ai_reg_nos'] = []
        current_ai_nos = df.at[index, 'images_status']['patent_status']['byregno']['ai_reg_nos']
        df.at[index, 'images_status']['patent_status']['byregno']['ai_reg_nos'] = list(set(current_ai_nos + pt_nos)) # Append unique

        valid_pt_nos = [clean_reg_no(reg_no) for reg_no in pt_nos if reg_no]
        valid_pt_nos = [reg_no for reg_no in valid_pt_nos if reg_no]
        reg_nos_to_process = [reg_no for reg_no in valid_pt_nos if reg_no not in ip_manager._state["patent"]["found_reg_nos"]]

        if reg_nos_to_process:
            log_message(f"    Processing {len(reg_nos_to_process)} new patent reg numbers...") # Removed 'from LLM'
            # Pass ip_manager to the function call
            get_and_process_patents_from_uspto_using_regnos(df, index, os.path.dirname(case_images_directory), reg_nos_to_process, ip_manager=ip_manager) # Pass ip_manager
        else:
            log_message(f"    Found patent reg numbers, but they were already processed or invalid.") # Removed 'LLM'


    langfuse.get_client().update_current_span(
        input={
            "CaseIndex": index,
            "NumPTNosToProcess": len(pt_nos),
            "Source": "LLM/MainDoc/Unresolved" # Assuming this is a fixed string based on the f-string
        },
        output={
            "PTNosProcessedForUSPTO": len(reg_nos_to_process) if 'reg_nos_to_process' in locals() else 0,
            "PatentGoalMetAfter": ip_manager.is_goal_met('patent')
        }
    )


@observe(capture_input=False, capture_output=False)
def create_plaintiff_images_view(plaintiff_df, case_directory, plaintiff_id):
    try:
        case_images_dir = os.path.join(case_directory, 'images')
        if os.path.exists(case_images_dir) and len(os.listdir(case_images_dir)) != 0:
            os.makedirs(local_plaintiff_folder, exist_ok=True)
            # Safely get plaintiff name
            if plaintiff_id is None:
                 log_message("    Warning: Plaintiff ID is None. Cannot create plaintiff view.")
                 return
            plaintiff_row = plaintiff_df[plaintiff_df['id'] == plaintiff_id]
            if not plaintiff_row.empty:
                 plaintiff_name = plaintiff_row['plaintiff_name'].values[0]
                 target_dir = os.path.join(local_plaintiff_folder, sanitize_name(plaintiff_name))
                 log_message(f"    Copying images to plaintiff view: {target_dir}")
                 shutil.copytree(case_images_dir, target_dir, dirs_exist_ok=True)
            else:
                 log_message(f"    Warning: Plaintiff ID {plaintiff_id} not found in plaintiff_df. Cannot create plaintiff view.")
        # else:
        #      log_message(f"    No images found in {case_images_dir} to copy to plaintiff view.")
    except Exception as e:
        print(f"\033[91mError creating plaintiff images view: {e}\033[0m")
        log_message(f"Error creating plaintiff images view: {e}")
    langfuse.get_client().update_current_span(
        input={
            "PlaintiffID": plaintiff_id,
            "CaseDirectory": os.path.basename(case_directory)
        },
        output={
            "PlaintiffViewCreated": 'Yes' if 'case_images_dir' in locals() and os.path.exists(case_images_dir) and len(os.listdir(case_images_dir)) != 0 and plaintiff_id is not None and ('plaintiff_row' in locals() and not plaintiff_row.empty) else 'No/Skipped',
            "TargetDir": os.path.basename(target_dir) if 'target_dir' in locals() and os.path.exists(target_dir) else 'N/A'
        }
    )

# Function to load and process images
def load_and_process_image(filepath, size):
    with Image.open(filepath) as img:
        img = img.convert('L')  # Convert to grayscale
        img = img.resize(size, Image.Resampling.LANCZOS)
        return np.array(img, dtype=np.float32)


def get_template(template_folder):
    template_name = os.path.basename(template_folder)
    hash_file = os.path.join(os.getcwd(), 'data', 'ImageTemplates', f"{template_name}_hash.pkl")
    size_file = os.path.join(os.getcwd(), 'data', 'ImageTemplates', f"{template_name}_size.pkl")

    force_update = False # Typically false unless template images change

    if not force_update and os.path.exists(hash_file) and os.path.exists(size_file):
        with open(hash_file, 'rb') as f:
            template_hash = pickle.load(f)
        with open(size_file, 'rb') as f:
            template_size = pickle.load(f)
    else:
        template_hash, template_size = create_template(template_folder)
        with open(hash_file, 'wb') as f:
            pickle.dump(template_hash, f)
        with open(size_file, 'wb') as f:
            pickle.dump(template_size, f)

    return template_hash, template_size


def create_template(template_folder):
   # Approach 1: Load template images and compute the average image
    template_images = []
    size = None

    # Approach 2: Compute a Representative Hash
    template_hashes = []
    hash_size = 8  # Default hash size for phash

    for filename in os.listdir(template_folder):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp', '.jpx')):
            filepath = os.path.join(template_folder, filename)
            try:
                with Image.open(filepath) as img:
                    if size is None:
                        size = img.size  # Set the size based on the first image
                    else:
                        img = img.resize(size, Image.Resampling.LANCZOS)
                    img_array = load_and_process_image(filepath, size)
                    template_images.append(img_array)

                    hash_val = imagehash.phash(img, hash_size=hash_size)
                    template_hashes.append(hash_val.hash.flatten()) # Store hash array
            except Exception as img_err:
                 log_message(f"Error processing template image {filepath}: {img_err}")


    if not template_images:
         raise ValueError(f"No valid images found or processed in template folder: {template_folder}")

    # Compute the hash of the average image
    average_image_array = np.mean(template_images, axis=0)
    average_image = Image.fromarray(average_image_array.astype('uint8'))
    average_hash = imagehash.phash(average_image)

    return average_hash, size


def initialize_case_image_status(df: pd.DataFrame, index: Any) -> None:
    df.at[index, 'images'] = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
    df.at[index, 'images_status'] = {
        'ip_manager_state': None,
        'steps_processed': [], 'number_of_pdfs': 0, 'trace_url': None,
        'copyright_status': {'exhibit': {'count': 0, 'steps': []}, 'byregno': {'ai_reg_nos': [], 'count': 0}, 'cn_website': {'cn_reg_nos': [], 'count': 0, 'sources': {}}, 'bygoogle': {'count': 0, 'search_term': ''}, 'manual': {'count': 0, 'comment': ''}},
        'trademark_status': {'exhibit': {'count': 0, 'steps': [], 'sources': {}}, 'byregno': {'ai_reg_nos': [], 'count': 0, 'steps': [], 'sources': {}}, 'cn_website': {'cn_reg_nos': [], 'count': 0, 'sources': {}}, 'byname': {'count': 0, 'search_term_used': '', 'sources': {}}, 'manual': {'count': 0, 'comment': ''}},
        'patent_status': {'exhibit': {'count': 0, 'steps': []}, 'byregno': {'ai_reg_nos': [], 'count': 0, 'steps': []}, 'cn_website': {'cn_reg_nos': [], 'count': 0}, 'byname': {'count': 0, 'search_term_used': '', 'ai_search_term': ''}, 'manual': {'count': 0, 'comment': ''}}
    }

if __name__ == "__main__":
    import zlib
    import base64
    # Example Usage (Illustrative - needs adaptation for new flow)
    print("Running example usage (needs adaptation for new iterative flow)...")
    try:
        df = get_table_from_GZ("tb_case", force_refresh=True) # Force refresh for testing
        df_db = df.copy() # Assuming df_db is for comparison/logging
        plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=True)

        # --- Example Iterative Flow ---
        # Select a case index to test
        test_docket = "1:24-cv-08816" # Patent in exhibit
        # test_docket = "1:24-cv-01358" # Patent reg no in complaint
        # test_docket = "1:25-cv-20593" # trademark : AI found reg numbers
        test_indices = df[df["docket"] == test_docket].index
        if not test_indices.empty:
            test_index = test_indices[0]
            log_message(f"--- Testing with index {test_index} for docket {test_docket} ---")

            # 1. Initialize case status (should happen once before loop in orchestrator)
            row = df.loc[test_index]
            case_directory = os.path.join(local_case_folder, sanitize_name(f"{pd.to_datetime(row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {row['docket']}"))
            copyright_template1_hash, copyright_template1_size = get_template(os.path.join(os.getcwd(), 'data', 'ImageTemplates', 'Copyright1'))

            # Initialize IPTrackingManager (Example: determine relevance based on NOS)
            nos_desc_main = row.get('nos_description', '').lower() # Use a different name to avoid conflict
            ip_manager = IPTrackingManager(
                is_trademark_relevant="trademark" in nos_desc_main,
                is_patent_relevant="patent" in nos_desc_main,
                is_copyright_relevant="copyright" in nos_desc_main
            )
            log_message(f"Initialized IP Manager: Relevant Goals = {ip_manager.get_relevant_goals()}")


            # Ensure columns exist and initialize
            if 'images' not in df.columns: df['images'] = pd.Series(dtype='object')
            if 'images_status' not in df.columns: df['images_status'] = pd.Series(dtype='object')
            if pd.isna(df.at[test_index, 'images']) or not isinstance(df.at[test_index, 'images'], dict):
                df.at[test_index, 'images'] = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
            if pd.isna(df.at[test_index, 'images_status']) or not isinstance(df.at[test_index, 'images_status'], dict):
                initialize_case_image_status(df, test_index)

            # 2. Simulate iterative download and process
            accumulated_ocr_data = {}
            processed_pdf_paths = []
            # Simulate finding PDF paths for steps (replace with actual paths from download)
            # Example: Find PDFs in the actual case directory for testing
            simulated_steps_data = []
            if os.path.exists(case_directory):
                 for step_dir in os.listdir(case_directory):
                      step_path = os.path.join(case_directory, step_dir)
                      if os.path.isdir(step_path) and step_dir != 'images':
                           pdfs_in_step = [os.path.join(step_path, f) for f in os.listdir(step_path) if f.lower().endswith('.pdf')]
                           if pdfs_in_step:
                                simulated_steps_data.append((step_dir, pdfs_in_step))

            if not simulated_steps_data:
                 log_message(f"No PDF steps found in {case_directory} for testing.")
            else:
                 log_message(f"Simulating processing for {len(simulated_steps_data)} steps found locally...")

                 # Initialize state for tracking missing reg numbers across steps

                 for step_nb, pdf_paths_for_step in simulated_steps_data:
                     log_message(f"\n--- Processing Step {step_nb} ---")
                     if not pdf_paths_for_step: continue

                     # Process this step's PDFs
                     ocr_data_step, is_rubbish = process_attachement_pdfs(
                         pdf_paths=pdf_paths_for_step,
                         df=df,
                         index=test_index,
                         case_directory=case_directory,
                         step_nb=step_nb,
                         copyright_template1_hash=copyright_template1_hash,
                         copyright_template1_size=copyright_template1_size,
                         ip_manager=ip_manager # Pass the manager
                     )

                     # Accumulate data (if needed elsewhere, otherwise just process)
                     accumulated_ocr_data.update(ocr_data_step)
                     processed_pdf_paths.extend(pdf_paths_for_step) # Keep track if needed

                     if is_rubbish:
                         log_message(f"Rubbish report detected in step {step_nb}. Stopping iterative search for this case.")
                         break # Stop processing this case if rubbish found

                     # Check if goals met after this step using the manager
                     if ip_manager.are_all_relevant_goals_met():
                         log_message(f"All relevant IP goals met after processing step {step_nb}. Stopping iterative search.")
                         break # Exit the loop for this case
                     else:
                          log_message(f"Relevant IP goals NOT met after step {step_nb}. Goals remaining: {ip_manager.get_unmet_relevant_goals()}")


                 # 3. Finalization (Always run to resize images etc.)
                 log_message("\n--- Running Finalization ---")                 
                 local_case_images_dir = os.path.join(case_directory, 'images')
                 os.makedirs(local_case_images_dir, exist_ok=True)
                #  secondary_ip_search(df, plaintiff_df, test_index, case_directory, local_case_images_dir, ip_manager)  # Chinese Website, Copyright Google and By Name
                 create_resized_images(df, test_index, case_directory, local_case_images_dir)
                 create_plaintiff_images_view(plaintiff_df, case_directory, df.loc[test_index,'plaintiff_id'])
            

                 log_message("\n--- Example finished. Review logs and DataFrame state. ---")
                 # print("\nFinal DataFrame state for index", test_index)
                 # print("Images:", df.loc[test_index, 'images'])
                 # print("Status:", df.loc[test_index, 'images_status'])
                 # print("File Status:", df.loc[test_index, 'file_status'])

                 # Optional: Save updated DataFrame
                 # insert_and_update_df_to_GZ_batch(df.loc[[test_index]], "tb_case", "id") # Save only the modified row for testing
                 # log_message("Updated test case saved.")

        else:
             print(f"Test docket {test_docket} not found in DataFrame.")

    except Exception as main_err:
        print(f"Error in example usage: {main_err}\n{traceback.format_exc()}")

    # Original test lines (commented out)
    # df = df[df["docket"].str.contains("1:25-cv-20593")]  # trademark : AI found reg numbers
    # ... other test cases ...
    # process_pictures_multiple_cases(df_db, df, plaintiff_df, force=True)
    # insert_and_update_df_to_GZ_batch(df, "tb_case", "id")
import gc
import zlib, base64, json, os, cv2, time, fitz, zipfile, asyncio, shutil, io, re, time, multiprocessing
import pandas as pd
from PIL import Image
import tempfile
from multiprocessing import Manager

from DatabaseManagement.Connections import get_gz_connection, is_connection_alive
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from Alerts.PicturesProcessing.ProcessPicturesShared import convert_page_number_to_image
start_time = time.time()
from IP.Trademarks.USPTO_TSDR_API import TSDRApi, format_reg_number
if multiprocessing.current_process().name == 'MainProcess':
    print(f"                TrademarkAPI TSDRApi after  {time.time()-start_time:.2f} seconds")
from Common.Constants import local_ip_folder, nas_ip_folder, sem_task
from FileManagement.NAS import NASConnection
from IP.ip_shared import safe_json_loads
from IP.Trademarks.Trademark_Database_NAS import get_trademark_from_NAS, send_trademark_to_nas, save_trademark_to_database, update_trademark_plaintiff_ids, create_full_list_of_reg_no


# ❌⚠️📥

def get_certificate_local_path(base_folder, ser_no=None, reg_no=None):
    """
    Constructs the certificate path using the new xx/yy/ser_no.webp structure or falls back to old reg_no structure.

    Args:
        base_folder: Base folder path (e.g., "Trademarks/Certificates")
        ser_no: Serial number (8 digits) - preferred for new structure
        reg_no: Registration number (6-7 digits) - fallback for old structure

    Returns:
        str: Full path to the certificate file
    """
    if ser_no and len(str(ser_no)) >= 4:
        # New structure: xx/yy/ser_no.webp
        ser_no_str = str(ser_no)
        xx = ser_no_str[-2:]  # Last 2 digits
        yy = ser_no_str[-4:-2]  # 2 digits before the last 2
        cert_dir = os.path.join(base_folder, xx, yy)
        cert_path = os.path.join(cert_dir, f"{ser_no}.webp")
    elif reg_no:
        # Old structure: reg_no.webp
        cert_path = os.path.join(base_folder, f"{reg_no}.webp")

    if cert_path:
        # Ensure the directory exists
        os.makedirs(os.path.dirname(cert_path), exist_ok=True)
        return cert_path

    return None


async def download_certificates_recursive(api_client, reg_nos_to_fetch, certificate_folder, depth=0):
    """
    Recursively download certificates using binary search to isolate problematic certificates.

    Args:
        api_client: TSDRApi client instance
        reg_nos_to_fetch: List of registration numbers to fetch
        certificate_folder: Folder to save certificates
        depth: Current recursion depth (for logging indentation)

    Returns:
        tuple: (successful_downloads_with_zip_files, failed_reg_nos)
        where successful_downloads_with_zip_files is a list of tuples: (reg_no, zip_filename)
    """
    indent = "  " * depth

    if not reg_nos_to_fetch:
        return [], []

    if len(reg_nos_to_fetch) == 1:
        # Base case: single certificate
        reg_no = reg_nos_to_fetch[0]
        print(f"{indent}📋 Trying single certificate: {reg_no}")

        try:
            reg_cert_pdf_content = await api_client.get_casedocs_bundle([reg_no], id_key='rn', format='zip', option='category=RC')

            if reg_cert_pdf_content:
                zip_filename = f"reg_cert_single_{reg_no}_{time.time()}.zip"
                saving_zip_result = await save_content(reg_cert_pdf_content, zip_filename, certificate_folder)

                if saving_zip_result:
                    print(f"{indent}✅ Single certificate {reg_no} downloaded successfully")
                    return [(reg_no, zip_filename)], []
                else:
                    print(f"{indent}❌ Failed to save certificate {reg_no}")
                    return [], [reg_no]
            else:
                print(f"\033[91m{indent}🚫 Certificate {reg_no} NOT AVAILABLE from USPTO\033[0m")
                return [], [reg_no]

        except Exception as e:
            print(f"\033[91m{indent}💥 Error downloading certificate {reg_no}: {e}\033[0m")
            return [], [reg_no]

    # Try full batch first
    print(f"{indent}📦 Trying batch of {len(reg_nos_to_fetch)} certificates...")

    try:
        reg_cert_pdf_content = await api_client.get_casedocs_bundle(reg_nos_to_fetch, id_key='rn', format='zip', option='category=RC')

        if reg_cert_pdf_content:
            zip_filename = f"reg_cert_batch_{len(reg_nos_to_fetch)}_{time.time()}.zip"
            saving_zip_result = await save_content(reg_cert_pdf_content, zip_filename, certificate_folder)

            if saving_zip_result:
                print(f"{indent}✅ Batch of {len(reg_nos_to_fetch)} certificates downloaded successfully")
                # Return all reg_nos with the same zip filename since they're all in one ZIP
                return [(reg_no, zip_filename) for reg_no in reg_nos_to_fetch], []
            else:
                print(f"{indent}❌ Failed to save batch of {len(reg_nos_to_fetch)} certificates")
        else:
            print(f"{indent}⚠️ Batch of {len(reg_nos_to_fetch)} certificates failed - splitting...")

    except Exception as e:
        print(f"{indent}💥 Error with batch of {len(reg_nos_to_fetch)} certificates: {e} - splitting...")

    # Batch failed, split in half and recurse
    mid = len(reg_nos_to_fetch) // 2
    left_half = reg_nos_to_fetch[:mid]
    right_half = reg_nos_to_fetch[mid:]

    print(f"{indent}🔄 Splitting batch: {len(left_half)} + {len(right_half)} certificates")

    # Recursively process both halves
    left_successful, left_failed = await download_certificates_recursive(api_client, left_half, certificate_folder, depth + 1)

    # Add delay between recursive calls to respect rate limits
    await asyncio.sleep(15)

    right_successful, right_failed = await download_certificates_recursive(api_client, right_half, certificate_folder, depth + 1)

    # Combine results
    all_successful = left_successful + right_successful
    all_failed = left_failed + right_failed

    return all_successful, all_failed

async def get_trademarks_uspto(full_list_of_reg_no, case_date=None, plaintiff_names=None): # Removed trademark_db_df from params
    api_client = TSDRApi()
    await api_client.start_session()

    base_folder = os.path.join(local_ip_folder, "Trademarks")
    xml_folder = os.path.join(base_folder, "XML")
    image_folder = os.path.join(base_folder, "Images")
    document_folder = os.path.join(base_folder, "Documents")
    status_folder = os.path.join(base_folder, "Status")
    certificate_folder = os.path.join(base_folder, "Certificates")
    os.makedirs(base_folder, exist_ok=True)
    os.makedirs(xml_folder, exist_ok=True)
    os.makedirs(image_folder, exist_ok=True)
    os.makedirs(certificate_folder, exist_ok=True)
    os.makedirs(document_folder, exist_ok=True)
    os.makedirs(status_folder, exist_ok=True)

    # Phase 1: Pre-processing Identifiers
    processed_identifiers = []
    for item_data in full_list_of_reg_no:
        current_formatted_reg_no = format_reg_number(item_data.get("reg_no"))
        if current_formatted_reg_no and "000" not in current_formatted_reg_no:
            processed_identifiers.append({"final_formatted_reg_no": current_formatted_reg_no, "final_formatted_ser_no": None, "plaintiff_id": item_data.get("plaintiff_id"), "metadata": item_data.get("metadata") })
        elif item_data.get("ser_no"):
            f_ser_no, f_reg_no_from_ser = await api_client.format_ser_number(item_data.get("ser_no"), case_date=case_date, plaintiff_names=plaintiff_names)
            processed_identifiers.append({"final_formatted_reg_no": f_reg_no_from_ser, "final_formatted_ser_no": f_ser_no, "plaintiff_id": item_data.get("plaintiff_id"), "metadata": item_data.get("metadata") })        


    # Phase 2: Batch Database Fetch
    relevant_trademarks_df = pd.DataFrame()
    if processed_identifiers:
        unique_reg_nos = list(set(item.get("final_formatted_reg_no") for item in processed_identifiers if item.get("final_formatted_reg_no")))
        unique_ser_nos = list(set(item.get("final_formatted_ser_no") for item in processed_identifiers if item.get("final_formatted_ser_no")))

        where_conditions = []
        if unique_reg_nos:
            quoted_reg_nos = [f"'{str(rn)}'" for rn in unique_reg_nos] # Basic SQL injection protection
            where_conditions.append(f"reg_no IN ({', '.join(quoted_reg_nos)})")
        if unique_ser_nos:
            quoted_ser_nos = [f"'{str(sn)}'" for sn in unique_ser_nos] # Basic SQL injection protection
            where_conditions.append(f"ser_no IN ({', '.join(quoted_ser_nos)})")

        final_where_clause = None
        if where_conditions:
            final_where_clause = " OR ".join(where_conditions)

        if final_where_clause:
            print(f"Fetching relevant trademarks from Database with WHERE: {final_where_clause}")
            relevant_trademarks_df = get_table_from_GZ(table_name="tb_trademark", where_clause=final_where_clause)
            if relevant_trademarks_df is None:
                relevant_trademarks_df = pd.DataFrame()
            elif not relevant_trademarks_df.empty:
                # Convert JSON string columns
                for col in ["int_cls", "country_codes", "associated_marks", "plaintiff_ids"]:
                    if col in relevant_trademarks_df.columns:
                        relevant_trademarks_df[col] = relevant_trademarks_df[col].apply(safe_json_loads)
            print(f"Fetched {len(relevant_trademarks_df)} relevant trademarks from DB.")
        else:
            print("No valid registration or serial numbers to query the database.")
            relevant_trademarks_df = pd.DataFrame() # Ensure it's an empty DataFrame
    else:
        print("No valid items to process after identifier formatting.")
        # Early exit if no items are valid, to avoid issues later
        await api_client.close_session()
        return pd.DataFrame(columns=["reg_no", "ser_no", "TRO", "applicant_name",
                                     "text", "int_cls", "date", "nb_suits", "country_codes",
                                     "associated_marks", "info_source", "image_source",
                                     "certificate_source", "plaintiff_id", "metadata"])


    # Create a new DataFrame to collect results that will be returned
    result_df = pd.DataFrame(columns=["reg_no", "ser_no", "TRO", "applicant_name",
                                      "text", "int_cls", "date", "nb_suits", "country_codes",
                                      "associated_marks", "info_source", "image_source",
                                      "certificate_source", "plaintiff_id", "metadata"])
    
    # Initialize NAS connection and database connection
    nas = NASConnection()
    db_connection = get_gz_connection() # This connection will be used by queue tasks
    
    time_last_request = time.time() # Keep for now, might be used by API client or other parts
    manager = Manager()
    certificate_needed = manager.list()  # Shared list
    
    # Create a queue for processing actions in a thread-safe manner
    queue = asyncio.Queue()  # Create the asyncio Queue
    
    # Set to track which trademarks we need to send to NAS
    nas_sync_needed = set()
    
    async def process_trademark(prepared_item, current_relevant_trademarks_df): # Renamed trademark_db_df
        nonlocal time_last_request, nas_sync_needed, certificate_needed # Added nas_sync_needed, certificate_needed
        
        formatted_reg_no = prepared_item.get("final_formatted_reg_no")
        formatted_ser_no = prepared_item.get("final_formatted_ser_no")
        plaintiff_id = prepared_item.get("plaintiff_id")
        metadata = prepared_item.get("metadata")

        # Check if trademark already exists in the fetched relevant_trademarks_df
        found_in_db = False
        found_on_nas = False
        db_entry_dict = None
        
        if formatted_reg_no and not current_relevant_trademarks_df.empty:
            match_df = current_relevant_trademarks_df[current_relevant_trademarks_df["reg_no"] == formatted_reg_no]
            if not match_df.empty:
                db_entry_dict = match_df.iloc[0].to_dict()
                found_in_db = True
        
        if not found_in_db and formatted_ser_no and not current_relevant_trademarks_df.empty:
            match_df = current_relevant_trademarks_df[current_relevant_trademarks_df["ser_no"] == formatted_ser_no]
            if not match_df.empty:
                db_entry_dict = match_df.iloc[0].to_dict()
                found_in_db = True
                # If found by ser_no, ensure formatted_reg_no is updated if db has it
                if pd.notna(db_entry_dict.get("reg_no")) and not formatted_reg_no:
                     formatted_reg_no = db_entry_dict.get("reg_no") # Update for consistency

        if found_in_db and db_entry_dict is not None:
            print(f"🔥 Trademark already in database: {formatted_reg_no}")
            # Convert dict to Series for get_trademark_from_NAS if it expects that
            df_entry_series = pd.Series(db_entry_dict)
            # Get ser_no from database entry if available
            db_ser_no = db_entry_dict.get("ser_no")
            found_on_nas, xml_local_path, image_local_path, certificate_local_path = await asyncio.to_thread(
                get_trademark_from_NAS, formatted_reg_no, df_entry_series, nas, db_ser_no
            )
            
            if plaintiff_id and found_on_nas:
                await queue.put(("update_plaintiff_id", {
                    "formatted_reg_no": formatted_reg_no,
                    "plaintiff_id": plaintiff_id
                }))
                
            if found_on_nas:
                # Add required fields that might not be in the database
                db_entry_dict["plaintiff_id"] = plaintiff_id
                db_entry_dict["metadata"] = metadata
                
                await queue.put(("add_to_result", db_entry_dict))
                return
        
        # If not found in relevant_trademarks_df => proceed with API calls: get info from XML file
        data = None # Initialize data here
        if formatted_reg_no and os.path.exists(os.path.join(xml_folder, f"{formatted_reg_no}.xml")): # If the XML file is on the HDD, use it
            with open(os.path.join(xml_folder, f"{formatted_reg_no}.xml"), 'rb') as f:
                xml_content_reg = f.read()
        else: # If the file is not on the HDD: get the XML from USPTO
            if formatted_reg_no: 
                xml_content_reg = await api_client.get_status_info_xml(formatted_reg_no, id_key='rn')
            elif formatted_ser_no: # This can be the case where we extracted a serial number (application number) from the exhibit or from searching the website by trademark owner name
                xml_content_reg = await api_client.get_status_info_xml(formatted_ser_no, id_key='sn')
        
        if xml_content_reg:
            if "<ns" in xml_content_reg.decode('utf-8'):
                data = api_client.process_xml_content(xml_content_reg)
            elif "{" in xml_content_reg.decode('utf-8')[:10]:
                data = api_client.process_json_content(xml_content_reg)
                
            # Ensure formatted_reg_no is updated/validated with data from XML
            formatted_reg_no = format_reg_number(data.get("reg_no")) # Ensure 7-digit padding
            if formatted_reg_no:
                await save_content(xml_content_reg, f"{formatted_reg_no}.xml", xml_folder)
            else:
                print(f"⚠️ Trademark is not valid, it was never approaved, it has no registration number. ser_no is {data.get("ser_no")}")
                return
        else:
            if formatted_reg_no: 
                print(f"⚠️ Trademark's XML file could not be downloaded from USPTO {formatted_reg_no}")
            elif formatted_ser_no:
                print(f"⚠️ Trademark's XML file could not be downloaded from USPTO {formatted_ser_no}")
            return

        # Create a new row based on API response data
        new_row = {}
        if data:
            # At this point, formatted_reg_no is the definitive 7-digit zero-padded version (or None if not found)
            new_row = {
                "reg_no": formatted_reg_no, "ser_no": data["application_number"],
                "TRO": True, "applicant_name": data["applicant_name"], "text": data["text"], "int_cls": data["int_cls"],
                "date": data["date"], "nb_suits": data["nb_suits"], "country_codes": data["country_codes"],
                "associated_marks": data["associated_marks"], "info_source": "USPTO_XML"
            }
            # Try to get image using URL
            image_downloaded, image_source = await get_image_from_url(api_client, formatted_reg_no, data, image_folder)
            if image_downloaded:
                new_row["image_source"] = image_source
        elif formatted_ser_no: # Only include essential fields when only serial number is available. When is this the case?
            new_row = {"reg_no": formatted_reg_no, "ser_no": formatted_ser_no, "TRO": True, "plaintiff_id": plaintiff_id}
            # Try to get image with just the ser_no
            image_downloaded, image_source = await get_image_from_url(api_client, formatted_reg_no, formatted_ser_no, image_folder)
            if image_downloaded:
                new_row["image_source"] = image_source
        else:
            return  # Cannot proceed without data or formatted_ser_no
            
        # If image couldn't be retrieved via URL, try other methods
        if "image_source" not in new_row or not new_row["image_source"]:
            # Try to get image from status zip
            content_reg = await api_client.get_status_content(formatted_reg_no, id_key='rn', format='zip')
            status_zip_path = os.path.join(status_folder, f"{formatted_reg_no}.zip")
            
            if await save_content(content_reg, f"{formatted_reg_no}.zip", status_folder):
                image_downloaded = await get_image_from_status_zip(formatted_reg_no, status_zip_path, status_folder, image_folder)
                if image_downloaded:
                    new_row["image_source"] = "USPTO_STATUSZIP"
                else: # If still no image, try documents zip
                    documents_zip = await api_client.get_casedocs_bundle([formatted_reg_no], id_key='rn', format='zip')
                    documents_zip_path = os.path.join(document_folder, f"{formatted_reg_no}.zip")
                    
                    if await save_content(documents_zip, f"{formatted_reg_no}.zip", document_folder):
                        image_downloaded, certificate_found = await get_image_from_documents_zip(formatted_reg_no, documents_zip_path, document_folder, image_folder, certificate_folder)
                        if image_downloaded:
                            new_row["image_source"] = "USPTO_DOCZIP"
                        if certificate_found:
                            new_row["certificate_source"] = "USPTO"

        # Add metadata if available
        if metadata:
            new_row["metadata"] = metadata

        # Check if certificate exists or is needed
        cert_path = get_certificate_local_path(certificate_folder, ser_no=new_row.get("ser_no"), reg_no=formatted_reg_no)
        cert_exists = os.path.exists(cert_path) if cert_path else False
        
        # Save to database and send to NAS if this is a new trademark
        if not found_on_nas:
            # Add to the sync needed set
            nas_sync_needed.add(formatted_reg_no)
            
            # Queue the database update (thread-safe)
            plaintiff_ids = [plaintiff_id] if plaintiff_id else []
            new_row["plaintiff_ids"] = plaintiff_ids
            
            # Add to result DataFrame through the queue first
            await queue.put(("add_to_result", new_row))

            if not cert_exists:
                # Defer saving until certificate is fetched
                print(f"📜 Certificate for {formatted_reg_no} needs fetching. Deferring save task.")
                certificate_needed.append({
                    "reg_no": formatted_reg_no,
                    "new_row": new_row.copy()
                })
            else:
                # Certificate exists, save immediately
                new_row["certificate_source"] = "USPTO"
                await queue.put(("save_trademark", {
                    "formatted_reg_no": formatted_reg_no,
                    "new_row": new_row
                }))
        elif cert_exists:
            new_row["certificate_source"] = "USPTO"
            
        # Check for certificate in TRO_ALL folder
        if os.path.exists(os.path.join(base_folder, "CertificatesFromTRO_ALL", f"{formatted_reg_no}.webp")):
            os.makedirs(os.path.join(base_folder, "CertificatesFromTRO_Found"), exist_ok=True)
            shutil.copy(
                os.path.join(base_folder, "CertificatesFromTRO_ALL", f"{formatted_reg_no}.webp"), 
                os.path.join(base_folder, "CertificatesFromTRO_Found", f"{formatted_reg_no}.webp")
            )
        
        # Process certificates in batches
        if len(certificate_needed) >= 20:
            # Limit to maximum 23 certificates as per API limitation
            certificate_needed_copy = list(certificate_needed)[:23]
            # Remove only what we're processing
            for cert in certificate_needed_copy:
                if cert in certificate_needed:
                    certificate_needed.remove(cert)
            await queue.put(("certificate", certificate_needed_copy))
        
        await asyncio.sleep(0)  # Yield control to allow queue processing

    async def process_queue():
        nonlocal result_df, relevant_trademarks_df # relevant_trademarks_df is passed to tasks, not modified here
        pending_nas_tasks = []  # Track NAS sync tasks
        pending_db_tasks = []   # Track database tasks
        
        while True:
            item = await queue.get()
            if item is None:  # Signal to stop
                # Wait for any pending tasks to complete before exiting
                pending_db_tasks = [t for t in pending_db_tasks if not t.done()] # Clean up completed database tasks
                if pending_db_tasks:
                    print(f"Waiting for {len(pending_db_tasks)} pending database tasks to complete...")
                    await asyncio.gather(*pending_db_tasks)
                
                pending_nas_tasks = [t for t in pending_nas_tasks if not t.done()] # Clean up completed NAS tasks
                if pending_nas_tasks:
                    print(f"Waiting for {len(pending_nas_tasks)} pending NAS tasks to complete...")
                    await asyncio.gather(*pending_nas_tasks)
                break
                
            item_type, item_data = item
            
            if item_type == "save_trademark":
                # Save to database
                formatted_reg_no = item_data["formatted_reg_no"]
                new_row = item_data["new_row"]
                
                # Create database save task and add to pending list
                # Pass relevant_tradema_rks_df (or a copy if modification is a concern, though it's not modified in save_trademark_to_database anymore)
                db_task = asyncio.create_task(save_trademark_to_database(formatted_reg_no, new_row, db_connection, relevant_trademarks_df.copy() if relevant_trademarks_df is not None else pd.DataFrame()))
                pending_db_tasks.append(db_task)
                
                # Create NAS sync task if needed
                if formatted_reg_no in nas_sync_needed:
                    # Get ser_no from new_row
                    ser_no = new_row.get("ser_no")
                    nas_task = asyncio.create_task(send_trademark_to_nas(formatted_reg_no, nas, local_ip_folder, nas_ip_folder, ser_no))
                    pending_nas_tasks.append(nas_task)
                
            elif item_type == "update_plaintiff_id":
                formatted_reg_no = item_data["formatted_reg_no"]
                plaintiff_id = item_data["plaintiff_id"]
                
                # Create database update task and add to pending list
                # Pass relevant_trademarks_df (or a copy)
                db_task = asyncio.create_task(update_trademark_plaintiff_ids(formatted_reg_no, plaintiff_id, db_connection, relevant_trademarks_df.copy() if relevant_trademarks_df is not None else pd.DataFrame()))
                pending_db_tasks.append(db_task)
   
            elif item_type == "add_to_result":
                # Add row to result DataFrame
                row_dict = item_data
                
                # Handle special fields and metadata
                row_for_df = row_dict.copy()
                
                # Add this row to the result DataFrame
                result_df = pd.concat([result_df, pd.DataFrame([row_for_df])], ignore_index=True)
                
                # If this is a duplicate, keep only the latest one
                if len(result_df[result_df["reg_no"] == row_for_df["reg_no"]]) > 1:
                    # Keep only the last row with this formatted_reg_no
                    duplicate_indices = result_df[result_df["reg_no"] == row_for_df["reg_no"]].index[:-1]
                    result_df = result_df.drop(duplicate_indices)
                
            elif item_type == "certificate":
                certificate_data_list = item_data  # Format: {"reg_no": formatted_reg_no, "new_row": new_row}
                if not certificate_data_list:
                    continue

                print(f"🔥🔥🔥 Starting recursive certificate download for {len(certificate_data_list)} certificates...")

                reg_nos_to_fetch = [item['reg_no'] for item in certificate_data_list]

                # Use recursive binary search to download certificates
                successful_downloads, failed_reg_nos = await download_certificates_recursive(
                    api_client, reg_nos_to_fetch, certificate_folder)

                print(f"🔥✅🔥 Recursive download completed: {len(successful_downloads)} successful, {len(failed_reg_nos)} failed")

                if failed_reg_nos:
                    print(f"\033[91m🚫 Failed to download certificates for: {', '.join(failed_reg_nos)}\033[0m")

                # Extract certificates from downloaded ZIP files
                all_extracted_certs = []
                if successful_downloads:
                    # Group downloads by ZIP file to avoid duplicate extractions
                    zip_files_processed = set()

                    for reg_no, zip_filename in successful_downloads:
                        if zip_filename not in zip_files_processed:
                            extracted_certs = unzip_and_extract_certificates(result_df, zip_filename, certificate_folder)
                            all_extracted_certs.extend(extracted_certs)
                            zip_files_processed.add(zip_filename)
                            print(f"🔥✅🔥 Extracted {len(extracted_certs)} certificates from {zip_filename}")

                # Process successfully extracted certificates
                if all_extracted_certs:
                    # Create a map to find the original data
                    data_map = {item['reg_no']: item['new_row'] for item in certificate_data_list}

                    # For each extracted certificate, queue the save task
                    for cert_reg_no in all_extracted_certs:
                        if cert_reg_no in data_map:
                            new_row = data_map[cert_reg_no]
                            new_row["certificate_source"] = "USPTO"

                            # Update certificate_source in the main result_df as well for consistency
                            idx = result_df[result_df["reg_no"] == cert_reg_no].index
                            if len(idx) > 0:
                                result_df.at[idx[0], "certificate_source"] = "USPTO"

                            # Queue the save task, which handles DB and NAS
                            await queue.put(("save_trademark", {
                                "formatted_reg_no": cert_reg_no,
                                "new_row": new_row
                            }))
                        else:
                            print(f"⚠️ Could not find original data for extracted certificate {cert_reg_no}")
                            
            pending_nas_tasks = [t for t in pending_nas_tasks if not t.done()] # Clean up completed NAS tasks
            pending_db_tasks = [t for t in pending_db_tasks if not t.done()] # Clean up completed database tasks
                    
            queue.task_done()

    # Start the queue processing task
    queue_task = asyncio.create_task(process_queue())

    # Use semaphore to limit concurrent API calls
    semaphore = asyncio.Semaphore(10)
    tasks = [sem_task(semaphore, process_trademark(prepared_item, relevant_trademarks_df)) for prepared_item in processed_identifiers]
    
    # Process all trademarks
    await asyncio.gather(*tasks)
    
    # Process any remaining certificates
    if len(certificate_needed) > 0:
        await queue.put(("certificate", list(certificate_needed)))

    # Signal the queue processing task to stop and wait for it
    await queue.put(None)
    await queue_task
    await api_client.close_session()
    
    # Close database connection
    if db_connection:
        db_connection.close()
        
    # Close NAS connection
    if nas:
        nas.close()

    return result_df


# Image retrieval functions
async def get_image_from_url(api_client, formatted_reg_no, data, image_folder):
    """
    Retrieves trademark image using the USPTO URL.
    
    Args:
        api_client: The USPTO API client
        formatted_reg_no: Formatted registration number
        data: Trademark data dictionary with image_url
        image_folder: Folder path to save the image
        
    Returns:
        tuple: (success_bool, image_source)
    """
    image_path = os.path.join(image_folder, f"{formatted_reg_no}.webp")
    
    if not os.path.exists(image_path):
        if isinstance(data, dict) and "image_url" in data:
            image_data = await api_client.download_from_uspto(data["image_url"])
            if image_data:
                image = Image.open(io.BytesIO(image_data))
                image.save(image_path, "WEBP")
                return True, "USPTO_URL"
            else:
                print(f"🔥 Error: No image found for {formatted_reg_no}")
                return False, None
        elif isinstance(data, str):  # If data is a string, it's the ser_no
            image_url = f"https://tsdr.uspto.gov/img/{data}/large"
            image_data = await api_client.download_from_uspto(image_url)
            if image_data:
                image = Image.open(io.BytesIO(image_data))
                image.save(image_path, "WEBP")
                return True, "USPTO_URL"
            else:
                print(f"🔥 Error: No image found for {formatted_reg_no}")
                return False, None
        else:
            print(f"🔥 Error: No image URL found for {formatted_reg_no}")
            return False, None
    else:
        if isinstance(data, dict) and "pure_img_location" in data:
            return True, "USPTO_URL"
        return True, "USPTO_URL"


async def get_image_from_status_zip(formatted_reg_no, zip_path, status_folder, image_folder):
    """
    Extracts trademark image from a status zip file.
    
    Args:
        formatted_reg_no: Formatted registration number
        zip_path: Path to the zip file
        status_folder: Folder path for extracted status files
        image_folder: Folder path to save the image
        
    Returns:
        bool: True if image extraction was successful
    """
    # Extract zip file
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        extracted_folder = os.path.join(status_folder, os.path.splitext(os.path.basename(zip_path))[0])
        zip_ref.extractall(extracted_folder)
    
    # Delete the zip file after extraction
    os.remove(zip_path)
    
    # Look for image files
    for file in os.listdir(extracted_folder):
        if file.lower().endswith(('.jpg', '.jpeg', '.png', '.webp', '.tif', '.tiff')):
            file_path = os.path.join(extracted_folder, file)
            if os.path.getsize(file_path) > 1024:  # Ensure file is not too small
                image = Image.open(file_path)
                image.save(os.path.join(image_folder, f"{formatted_reg_no}.webp"), "WEBP")
                print(f"✅ Image extracted from status zip for {formatted_reg_no}")
                return True
            else:
                print(f"🔥 Error: File {file} is too small to be an image")
    
    print(f"🔥 Error: No image found in status zip for {formatted_reg_no}")
    return False


async def get_image_from_documents_zip(formatted_reg_no, zip_path, document_folder, image_folder, certificate_folder=None, ser_no=None):
    """
    Extracts trademark image and potentially certificate from a documents zip file.
    
    Args:
        formatted_reg_no: Formatted registration number
        zip_path: Path to the zip file
        document_folder: Folder path for extracted document files
        image_folder: Folder path to save the image
        certificate_folder: Optional folder path to save the certificate
        ser_no: Serial number, used for new path format
        
    Returns:
        tuple: (image_found_bool, certificate_found_bool)
    """
    # Extract zip file
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        extracted_folder = os.path.join(document_folder, os.path.splitext(os.path.basename(zip_path))[0])
        zip_ref.extractall(extracted_folder)
    
    # Delete the zip file after extraction
    os.remove(zip_path)
    
    image_found = False
    certificate_found = False
    
    # Process the extracted files
    for subfolder in os.listdir(extracted_folder):
        if not subfolder.isdigit():  # Skip non-numeric folders
            continue
            
        ser_no_folder = os.path.join(extracted_folder, subfolder)
        documents_folder_in_zip = os.path.join(ser_no_folder, "Documents")
        
        if not os.path.exists(documents_folder_in_zip):
            print(f"🔥 Error: No Documents folder found in {ser_no_folder}")
            continue
        
        # Look for drawing folder
        for folder_name in os.listdir(documents_folder_in_zip):
            # Process drawing images
            if "Drawing" in folder_name:
                drawing_folder = os.path.join(documents_folder_in_zip, folder_name)
                
                for image_file in os.listdir(drawing_folder):
                    if image_file.lower().endswith(('.png', '.jpg', '.jpeg', '.tiff', '.tif', '.webp')):
                        try:
                            image_path = os.path.join(drawing_folder, image_file)
                            image = Image.open(image_path)
                            image.save(os.path.join(image_folder, f"{formatted_reg_no}.webp"), "WEBP")
                            print(f"✅ Image extracted from documents zip for {formatted_reg_no}")
                            image_found = True
                            break
                        except Exception as e:
                            print(f"🔥 Error copying image from zip: {e}")
                
            # Process certificate if certificate_folder is provided
            if certificate_folder and "registration cert" in folder_name.lower():
                print(f"✅ Found registration certificate in Document Zip for {formatted_reg_no}")
                zip_certificate_folder = os.path.join(documents_folder_in_zip, folder_name)
                
                # Determine the certificate path based on the new logic
                cert_save_path = get_certificate_local_path(certificate_folder, ser_no, formatted_reg_no)

                for file in os.listdir(zip_certificate_folder):
                    if file.endswith(".pdf"):
                        with fitz.open(os.path.join(zip_certificate_folder, file)) as pdf_document:
                            im_array = convert_page_number_to_image(pdf_document, 1)
                            cv2.imwrite(cert_save_path, im_array) # Use cert_save_path
                            certificate_found = True
                            break
                    elif file.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png')):
                        tif_path = os.path.join(zip_certificate_folder, file)
                        im_array = cv2.imread(tif_path)
                        cv2.imwrite(cert_save_path, im_array) # Use cert_save_path
                        certificate_found = True
                        break
    
    if not image_found:
        print(f"🔥 Error: No image found in Drawing folder in Document Zip for {formatted_reg_no}")
    
    return image_found, certificate_found


def unzip_and_extract_certificates(df_trademarks, zip_filename, certificate_folder):
    zip_path = os.path.join(certificate_folder, zip_filename)
    extracted_reg_nos = []
    
    temp_dir_path = None  # Initialize to None
    try:
        temp_dir_path = tempfile.mkdtemp() # Create temp dir manually
        unzip_uspto(temp_dir_path, zip_path) # Use temp_dir_path

        for folder in os.listdir(temp_dir_path):  # Use temp_dir_path
            zip_reg_no_folder = os.path.join(temp_dir_path, folder) # Use temp_dir_path
            
            # Find the row in the dataframe
            # Preserving original logic for finding df_row, with re.escape for initial attempts
            # Convert ser_no to string to avoid "Can only use .str accessor with string values" error
            df_row = df_trademarks[(df_trademarks["ser_no"].notna()) & (df_trademarks["ser_no"].astype(str).str.contains(re.escape(folder)))]
            if len(df_row) == 0:
                df_row = df_trademarks[(df_trademarks["image_source"].notna()) & (df_trademarks["image_source"].str.contains(re.escape(folder)))]

            if len(df_row) == 0:
                df_row = df_trademarks[(df_trademarks["ser_no"].notna()) & (df_trademarks["ser_no"].astype(str).str.contains(folder))]
                if len(df_row) == 0:
                    df_row = df_trademarks[(df_trademarks["image_source"].notna()) & (df_trademarks["image_source"].str.contains(folder))]
                    if len(df_row) == 0:
                        df_na = df_trademarks[df_trademarks["image_source"].isna()]
                        df_row = df_na[df_na["ser_no"].apply(lambda x: str(x) in folder)]
                        if len(df_row) == 0:
                            df_row = df_trademarks[df_trademarks["reg_no"].apply(lambda x: str(x) in folder)]
                            if len(df_row) == 0:
                                print(f"🔥❌ Error: Found {len(df_row)} rows for {os.path.basename(zip_reg_no_folder)} in df_trademarks")
                                continue
            
            df_row_index = df_row.index[0]
            formatted_reg_no = df_row["reg_no"].iloc[0]
            ser_no = df_row["ser_no"].iloc[0] # Get ser_no for new path logic
            
            zip_documents_folder = os.path.join(zip_reg_no_folder, os.listdir(zip_reg_no_folder)[0])

            # Reverse order: take the last certificate
            for cert_folder_name in os.listdir(zip_documents_folder)[::-1]: # Renamed cert_folder to cert_folder_name to avoid conflict
                try:
                    zip_certificate_folder_path = os.path.join(zip_documents_folder, cert_folder_name) # Renamed
                    file = sorted(os.listdir(zip_certificate_folder_path))[0]

                    # Determine the certificate path based on the new logic
                    cert_path = get_certificate_local_path(certificate_folder, ser_no, formatted_reg_no)

                    if file.endswith(".pdf"):
                        pdf_file_path = os.path.join(zip_certificate_folder_path, file)
                        pdf_document = None
                        im_array = None
                        processed_successfully = False
                        try:
                            pdf_document = fitz.open(pdf_file_path)
                            im_array = convert_page_number_to_image(pdf_document, 1)
                            if im_array is not None:
                                cv2.imwrite(cert_path, im_array) # Use cert_path
                                processed_successfully = True
                            else:
                                print(f"🔥❌ Warning: convert_page_number_to_image returned None for {pdf_file_path}")
                        except Exception as e_pdf_proc:
                            print(f"🔥❌ Error processing PDF {pdf_file_path} for cert: {e_pdf_proc}")
                        finally:
                            if pdf_document:
                                pdf_document.close()
                                del pdf_document
                            if im_array is not None:
                                del im_array
                            gc.collect()

                        if processed_successfully:
                            df_trademarks.at[df_row_index, "certificate_source"] = "USPTO"
                            extracted_reg_nos.append(formatted_reg_no) # Add to list
                            break
                    elif file.endswith((".tif", ".tiff", ".jpg", ".jpeg", ".png")):
                        tif_path = os.path.join(zip_certificate_folder_path, file)
                        im_array = cv2.imread(tif_path)
                        if im_array is not None:
                             cv2.imwrite(cert_path, im_array) # Use cert_path
                             df_trademarks.at[df_row_index, "certificate_source"] = "USPTO"
                             extracted_reg_nos.append(formatted_reg_no) # Add to list
                             del im_array # Clean up
                             gc.collect() # Explicit gc after del
                             break
                        else:
                            print(f"🔥❌ Warning: cv2.imread returned None for {tif_path}")
                    else:
                        print(f"🔥❌ Error: Found {file} in {zip_certificate_folder_path}: no pdf or tif file")
                        continue
                except Exception as e:
                    print(f"🔥❌ Something went wrong in extracting the certificate: {e}") # Simplified error message
        
    finally:
        if temp_dir_path and os.path.exists(temp_dir_path):
            max_retries = 5
            retry_delay_seconds = 2
            for attempt in range(max_retries):
                try:
                    shutil.rmtree(temp_dir_path)
                    print(f"Successfully removed temporary directory: {temp_dir_path}")
                    break
                except PermissionError as e:
                    print(f"Attempt {attempt + 1} to remove {temp_dir_path} failed: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay_seconds)
                        retry_delay_seconds *= 2
                    else:
                        print(f"Failed to remove temporary directory {temp_dir_path} after {max_retries} attempts.")
                except Exception as e:
                    print(f"An unexpected error occurred while removing {temp_dir_path}: {e}")
                    break

    os.remove(zip_path)
    return extracted_reg_nos # Return the list


def unzip_uspto(temp_dir, zip_path):
# Why do we need this fundtion? Zip files from USPTO are malformed with some path using \ while others use / -> this works well on windows, not not on linux
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            for member_info in zip_ref.infolist():
                # Fix the path separators for the target OS
                target_path_parts = member_info.filename.split('\\')
                # Handle potential drive letters if created weirdly on Windows (e.g., C:\...)
                if len(target_path_parts) > 1 and target_path_parts[0].endswith(':'):
                    target_path_parts = target_path_parts[1:] # Remove drive letter part

                # Rejoin using the correct OS separator
                target_path = os.path.join(temp_dir, *target_path_parts)

                # Ensure parent directory exists
                if member_info.is_dir():
                    os.makedirs(target_path, exist_ok=True)
                else:
                    # Extract file: make sure parent dir exists first
                    parent_dir = os.path.dirname(target_path)
                    if parent_dir:
                        os.makedirs(parent_dir, exist_ok=True)

                    # Extract the file content to the corrected path
                    # Use extract method to get file content, then write manually
                    # This avoids potential issues with extract() trying to use the bad name
                    with zip_ref.open(member_info.filename) as source, \
                        open(target_path, "wb") as target:
                        target.write(source.read())

    except zipfile.BadZipFile:
        print(f"Error: {zip_path} is not a valid zip file or is corrupted.")
    except FileNotFoundError:
        print(f"Error: {zip_path} not found.")
    except Exception as e:
        print(f"An error occurred during extraction: {e}")


async def save_content(content, filename, folder, is_xml=False):
    if content:
        filepath = os.path.join(folder, filename)
        try:
            with open(filepath, 'wb') as f:
                f.write(content)
            print(f"☑️ Saved {filename} to {filepath}")
            return True
        except Exception as e:
            print(f"🔥 Error saving {filename}: {e}")
            return False
    else:
        print(f"⚠️ No content received for {filename}, not saving.")
        return False
    

if __name__ == "__main__":
    df_cases = get_table_from_GZ("tb_case", force_refresh=False)
    df_plaintiff = get_table_from_GZ("tb_plaintiff", force_refresh=False)
    full_list_of_reg_no = create_full_list_of_reg_no(df_cases)
    
    # Process all trademarks using our optimized approach
    # This will update the database and return only the trademarks requested
    df_result = asyncio.run(get_trademarks_uspto(full_list_of_reg_no))
    
    print(f"Processed {len(df_result)} trademarks")
    
    # No need to update the database as it's already done in get_trademarks_uspto

    # Test with a subset for debugging
    # test_reg_numbers = [
    #     {"reg_no": "0626035", "plaintiff_id": 1}, 
    #     {"reg_no": "0902190", "plaintiff_id": 1}, 
    #     {"reg_no": "1177400", "plaintiff_id": 1}
    # ]
    # asyncio.run(get_trademarks_uspto(test_reg_numbers))

import os, sys,  os, time, json, time
sys.path.append(os.getcwd())

from logdata import log_message
from IP.Trademarks.Trademark_API import get_trademarks_uspto
from IP.Trademarks.Trademark_Web import uspto_get_reg_by_owner
from langfuse import observe
import langfuse
from Alerts.IPTrackingManager import IPTrackingManager # Added import
from Alerts.PicturesProcessing.Trademarks_RegNo import process_trademark_data
from Alerts.Plaintiff import generate_search_terms_for_ip # For uniform search logic


@observe(capture_input=False, capture_output=False)
async def get_trademark_data_by_name(df, index, case_images_directory, plaintiff_df, ip_manager: IPTrackingManager):
    """
    Searches for trademarks by plaintiff name using USPTO web scraping and API.

    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        case_images_directory: Directory to save extracted images.
        plaintiff_df: DataFrame containing plaintiff information.
        ip_manager: Instance of IPTrackingManager to track IP processing state.
    """
    log_message(f"        Initiating trademark search by name for case index {index}...")
    plaintiff_id = df.at[index, "plaintiff_id"]
    main_plaintiff_name_row = plaintiff_df[plaintiff_df["id"] == plaintiff_id]

    if main_plaintiff_name_row.empty:
        log_message(f"        - Main Plaintiff ID {plaintiff_id} not found in plaintiff_df. Cannot effectively search by name.", level='WARNING')
        return False
    main_plaintiff_name = main_plaintiff_name_row["plaintiff_name"].values[0]

    plaintiff_names_json_str = df.at[index, "plaintiff_names"]
    final_search_terms = generate_search_terms_for_ip(main_plaintiff_name, plaintiff_names_json_str)

    if not final_search_terms:
        log_message(f"        - No suitable search terms generated for trademark search by name.", level='INFO')
        return False

    best_serial_list = None
    best_owner_list = None
    search_term_used_for_results = None

    for current_search_term in final_search_terms:
        log_message(f"        - Searching USPTO for trademarks by owner: '{current_search_term}' (max 100 results)")
        # uspto_get_reg_by_owner returns serial numbers
        serial_list, owner_list = uspto_get_reg_by_owner(current_search_term, max_results=100)
        log_message(f"        - Found {len(serial_list)} trademarks for '{current_search_term}' with {len(owner_list)} unique owners.")

        if len(serial_list) > 0 and (best_serial_list is None or len(owner_list) < len(best_owner_list)): # Keep the first set of results as a fallback
            best_serial_list = serial_list
            best_owner_list = owner_list
            search_term_used_for_results = current_search_term

        if len(owner_list) == 1:
            log_message(f"        - Single owner found for '{current_search_term}'. Using these {len(serial_list)} results.")
            break # Found suitable results
        else:
            log_message(f"        - Multiple owners found for '{current_search_term}' (owner_list: {owner_list}). Continuing if more terms available.")

    if not best_serial_list:
        log_message(f"        - No trademarks found after trying all search terms.", level='INFO')
        return False

    log_message(f"        - Proceeding with {len(best_serial_list)} trademarks found using search term '{search_term_used_for_results}'.")

    start_time = time.time()
    trademark_list = []
    for ser_no in best_serial_list:
        metadata = {
            'trademark_filename': f"{ser_no}.webp",
            'full_filename': f"{ser_no}_full.webp"
        }
        trademark_list.append({"ser_no": ser_no, "plaintiff_id": plaintiff_id, "metadata": metadata})

    success = False
    if trademark_list:
        df_trademarks = await get_trademarks_uspto(trademark_list)
        log_message(f"        - USPTO Trademark API fetched {len(df_trademarks)} full records in {time.time() - start_time:.1f}s.")

        for _, trademark in df_trademarks.iterrows():
            if "metadata" in trademark:
                metadata = trademark.get("metadata", {}) # Use .get for safety
                if "trademark_filename" in metadata:
                    # Pass ip_manager to process_trademark_data
                    if process_trademark_data(df, index, case_images_directory, trademark, metadata, 'byname', ip_manager):
                        success = True
                        # State update happens within process_trademark_data
        if success:
            df.at[index, 'validation_status'] = 'review_required'
            df.at[index, 'images_status']['trademark_status']['byname']['search_term_used'] = search_term_used_for_results
            df.at[index, 'images_status']['trademark_status']['byname']['count'] = len(best_serial_list) # Or count successful process_trademark_data

    langfuse.get_client().update_current_span(
        input={
            "SearchTerms": final_search_terms
        },
        output={
            "NameUsedForSearch": search_term_used_for_results if search_term_used_for_results else 'N/A',
            "FinalSerialListCount": len(best_serial_list) if best_serial_list else 0,
            "FinalOwnerList": best_owner_list if best_owner_list else "N/A",
            "TrademarksFromUSPTO_API": len(df_trademarks) if 'df_trademarks' in locals() else 0,
            "SuccessfullyProcessedByName": success,
            "TrademarkGoalMetAfter": ip_manager.is_goal_met('trademark')
        }
    )
    return success
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Check History</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='api_studio/api_studio.css') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <!-- Load modular JavaScript files -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="{{ url_for('static', filename='api_studio/utils.js') }}"></script>
    <script src="{{ url_for('static', filename='api_studio/language.js') }}"></script>
    <script src="{{ url_for('static', filename='api_studio/results-display.js') }}"></script>
    <script src="{{ url_for('static', filename='api_studio/check-history.js') }}"></script>
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
</head>
<body>
    <header class="header">
        <div class="logo-title-container">
            <img src="{{ url_for('static', filename='images/maidalv_logo.png') }}" alt="API Studio Logo" class="logo" style="height: 40px; margin-right: 15px;">
            <a href="https://www.maidalv.com" style="text-decoration: none; color: inherit;"><h1 class="studio-title">Maidalv API Studio (v2b6)</h1></a>
        </div>
        <nav>
            <ul>
                <li><a href="/api_studio" data-i18n="check_page">Check</a></li>
                <li><a href="/check_history" data-i18n="check_history_page">Check History</a></li>
                <li><a href="/api_studio_reverse_check" data-i18n="reverse_check_page">Reverse Check</a></li>
                <li><a href="{{ url_for('static', filename='api_demo.py') }}" download="api_demo.py" id="getCodeButton" data-i18n="get_code">Get Code</a></li>
                <li>
                    <select id="languageSelect" style="padding: 5px; border-radius: 4px;">
                        <option value="en">English</option>
                        <option value="zh">中文</option>
                    </select>
                </li>
            </ul>
        </nav>
    </header>

    <div class="container main-content">
        <div class="left">
            <h2 data-i18n="check_history">Check History</h2>
            <div class="form-group">
                <label for="api_key" data-i18n="api_key">API Key:</label><span class="required-field">*</span>
                <input type="text" id="api_key" name="api_key">
                <button type="button" id="fetchHistoryBtn" data-i18n="fetch_history">Fetch History</button>
            </div>
            <div id="historyControls" style="display: none;">
                <div class="form-group">
                    <label for="datePicker" data-i18n="select_date">Select Date:</label>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <button id="prevDateBtn"><</button>
                        <input type="text" id="datePicker" placeholder="Select a date">
                        <button id="nextDateBtn">></button>
                    </div>
                </div>
                <div id="checkList" class="reverse-check-list">
                    <!-- Check history will be loaded here -->
                </div>
            </div>
        </div>
        <div class="right">
            <h2 data-i18n="check_details">Check Details</h2>
            <div id="output">
                <div id="request-info"></div>
                <hr/>
                <div id="results-display"></div>
            </div>
        </div>
    </div>
    <input type="hidden" id="language" name="language" value="en">
</body>
</html>
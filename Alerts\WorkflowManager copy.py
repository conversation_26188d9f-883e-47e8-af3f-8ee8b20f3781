from Alerts.ReprocessCases import reprocess_cases
from Alerts.LexisNexis.Scrape_Date_Range import scrape_date_range
from datetime import date, timedelta
import asyncio
from logdata import log_message

def fetch(nb_days=1):
    start_date = date.today() - timedelta(days=nb_days)
    end_date = date.today()
    
    cases_df = get_table_from_GZ("tb_case", force_refresh=True)
    plaintiffs_df = get_table_from_GZ("tb_plaintiff", force_refresh=True)
    

    # start_date = date(2022, 10, 12)
    # end_date = date(2022, 10, 15)
    new_cases_df = scrape_date_range(start_date, end_date, cases_df, plaintiffs_df)

    # Check if we have any new cases to process
    if new_cases_df.empty:
        log_message(f"No new cases found for date range {start_date} to {end_date}", level="INFO")
        empty_tracking_dict = {
            'cases_no_ip_then_some_ip': 0,
            'cases_no_ip_then_all_ip': 0,
            'cases_some_ip_then_more_ip': 0,
            'cases_some_ip_then_all_ip': 0,
            'cases_ip_regressed': 0,
            'cases_already_all_ip': 0,
            'total_count': 0
        }
        return new_cases_df, empty_tracking_dict

    log_message(f"Found {len(new_cases_df)} new cases to process", level="INFO")
    
    processing_options = {
        'update_steps': True, # Forces update of case steps from source
        'process_pictures': True, # Process images from PDFs, always true for now!
        'upload_files_nas': True, # Upload files to NAS
        'upload_files_cos': True, # Upload files to COS
        'run_plaintiff_overview': True, # Run AI plaintiff overview
        'run_summary_translation': True, # Run AI summary translation
        'run_step_translation': True, # Run AI step translation
        'save_to_db': True, # Save results to DB
        'processing_mode': 'resume', # Processing mode: 'full_reprocess' (default) or 'resume'
        'refresh_days_threshold': 15 # Refresh threshold in days
    }
    
    exisitng_case_to_continue = cases_df where status != "closed"
    
    exisitng_case_to_continue = exisitng_case_to_continue.merge(new_cases_df)
    

    success_status, tracking_dict = asyncio.run(
        reprocess_cases(
            cases_to_reprocess=new_cases_df, 
            processing_options=processing_options, 
            trace_name="Daily Fetch Workflow",
            full_cases_df=cases_df,
            plaintiff_df=plaintiffs_df
        )
    )
    
    return new_cases_df, tracking_dict

if __name__ == '__main__':
    fetch()
import asyncio
import aiohttp
import os
import json
import zlib
import numpy as np
from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct, VectorParams, Distance
from logdata import log_message
import base64
from Common.uuid_utils import generate_uuid


BATCH_SIZE = 50

class EmbeddingQueue:
    def __init__(self, max_concurrent=5, num_workers=10):
        self.queue = asyncio.Queue()
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.client = QdrantClient(url=os.environ.get("QDRANT_URL"), api_key=os.environ.get("QDRANT_API_KEY"), timeout=30)
        self.collection_name = "IP_Assets"
        self.num_workers = num_workers
        self.workers = []
        self.shutdown_event = asyncio.Event()
        self._ensure_collection()

    async def drain(self):
        """Wait until queue is empty and all tasks are processed"""
        await self.queue.join()
        log_message("All embedding queue tasks completed", level='INFO')
    
    def _ensure_collection(self):
        """Create collection if it doesn't exist"""
        collections = self.client.get_collections().collections
        if not any(c.name == self.collection_name for c in collections):
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(size=1024, distance=Distance.COSINE)
            )
            log_message(f"Created Qdrant collection: {self.collection_name}", level='INFO')
    
    
    async def process_batch(self, batch):
        """Process batch of images"""
        # Batch check for existing embeddings
        point_ids = [item[1] for item in batch]
        try:
            existing_points = self.client.retrieve(
                collection_name=self.collection_name,
                ids=point_ids,
                with_vectors=False
            )
            existing_ids = {point.id for point in existing_points}
        except Exception as e:
            log_message(f"Qdrant batch check failed: {str(e)}", level='ERROR')
            existing_ids = set()
        
        to_process = []
        for item in batch:
            ip_type, point_id, image_path, ser_no = item
            if point_id not in existing_ids:
                to_process.append(item)
            # else:
            #     log_message(f"Skipping existing embedding: {ser_no}", level='DEBUG')
        
        if not to_process:
            return
        
        # Prepare API request
        API_URL = "https://api.maidalv.com/get_image_embeddings"
        API_KEY = os.getenv("API_BEARER_TOKEN")
        headers = {"Content-Type": "application/json"}
        
        images_base64 = []
        valid_items = []
        for ip_type, point_id, image_path, ser_no in [(item[0], item[1], item[2], item[3]) for item in to_process]: # Extract point_id and ser_no
            if os.path.exists(image_path):
                try:
                    with open(image_path, "rb") as img_file:
                        images_base64.append(base64.b64encode(img_file.read()).decode('utf-8'))
                    valid_items.append((ip_type, point_id, ser_no))
                except Exception as e:
                    log_message(f"Error reading {image_path}: {str(e)}", level='ERROR')
            else:
                log_message(f"Image missing: {image_path}", level='WARNING')
        
        if not images_base64:
            return
        
        payload = {
            "api_key": API_KEY,
            "images": images_base64
        }
        
        # Get embeddings
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(API_URL, json=payload, headers=headers) as resp:
                    if resp.status != 200:
                        log_message(f"API error: {resp.status} {await resp.text()}", level='ERROR')
                        return
                    
                    result = await resp.json()
                    if result.get('status') == 'success':
                        # Decode and decompress embeddings
                        encoded_compressed_embeddings = result.get('embeddings')
                        compressed_embeddings = base64.b64decode(encoded_compressed_embeddings)
                        embeddings_bytes = zlib.decompress(compressed_embeddings)
                        
                        # Convert bytes back to numpy array
                        embeddings = np.frombuffer(embeddings_bytes, dtype=np.float32).reshape(-1, 1024)
                        
                        if len(embeddings) != len(valid_items):
                            log_message(f"Embedding count mismatch: expected {len(valid_items)}, got {len(embeddings)}", level='ERROR')
                            return
                        
                        points = [
                            PointStruct(
                                id=item[1],
                                vector={"siglip_vector": embedding.tolist()},  # Convert numpy array to list
                                payload={
                                    "ip_type": item[0],
                                    "reg_no": item[2]
                                }
                            )
                            for item, embedding in zip(valid_items, embeddings)
                        ]
                        self.client.upsert(
                            collection_name=self.collection_name,
                            points=points,
                            wait=True
                        )
                        queue_size = self.queue.qsize()
                        log_message(f"Uploaded {len(points)} embeddings to Qdrant, {queue_size} items remaining in queue", level='INFO')
                    else:
                        log_message(f"Embedding API error: {result.get('message', 'Unknown error')}", level='ERROR')
        except Exception as e:
            log_message(f"Embedding processing failed: {str(e)}", level='ERROR')
    
    async def enqueue(self, ip_type, ser_no, image_path):
        """Add image to processing queue (individual enqueue - use batch_enqueue for better performance)"""
        if not ser_no:
            log_message("Skipping item with missing ser_no", level='WARNING')
            return False

        if ip_type == "Trademark":
            point_id = generate_uuid(ser_no)
        elif ip_type == "Patent":
            point_id = generate_uuid(os.path.splitext(os.path.basename(image_path))[0]) # Use filename without extension as ID for patents
        await self.queue.put((ip_type, point_id, image_path, ser_no))
        # log_message(f"Enqueued embedding for {ser_no}, queue size: {self.queue.qsize()}", level='DEBUG')
        return True

    async def batch_enqueue(self, items, batch_size=1000):
        """
        Efficiently enqueue multiple items after batch checking for existing embeddings

        Args:
            items: List of tuples (ser_no, image_path)
            batch_size: Size of batches for Qdrant existence checks

        Returns:
            tuple: (enqueued_count, skipped_count)
        """
        if not items:
            return 0, 0

        # Generate point IDs for all items
        if items[0][0] == "Trademark":
            items_with_ids = [(ip_type, generate_uuid(ser_no), image_path, ser_no) for ip_type, ser_no, image_path in items if ser_no]
        elif items[0][0] == "Patent":
            items_with_ids = [(ip_type, generate_uuid(os.path.splitext(os.path.basename(image_path))[0]), image_path, reg_no) for ip_type, reg_no, image_path in items if reg_no]

        if not items_with_ids:
            log_message("No valid items to enqueue", level='WARNING')
            return 0, 0

        all_point_ids = [item[1] for item in items_with_ids]
        existing_ids = set()

        # Batch check for existing embeddings
        log_message(f"Checking existence of {len(all_point_ids)} embeddings in batches of {batch_size}", level='INFO')

        for i in range(0, len(all_point_ids), batch_size):
            batch_ids = all_point_ids[i:i + batch_size]
            try:
                existing_points = self.client.retrieve(
                    collection_name=self.collection_name,
                    ids=batch_ids,
                    with_vectors=False
                )
                existing_ids.update(point.id for point in existing_points)
            except Exception as e:
                log_message(f"Qdrant batch check failed for batch {i//batch_size + 1}: {str(e)}", level='WARNING')
                # Continue without adding to existing_ids if check fails

        # Enqueue only items that don't already exist
        enqueued_count = 0
        skipped_count = 0

        for ip_type, point_id, image_path, num in items_with_ids:
            if point_id not in existing_ids:
                await self.queue.put((ip_type, point_id, image_path, num))
                enqueued_count += 1
            else:
                # One off update of the reg_no payload for patents: only usefull if later on I want to change the reg number for design patent to be without 0 (USPTO API format) to be with 0 (USPTO bulk format)
                # try:
                #     self.client.set_payload(
                #         collection_name=self.collection_name,
                #         payload={"reg_no": num},
                #         points=[point_id]
                #     )
                # except Exception as e:
                #     log_message(f"Failed to update reg_no for patent {num}: {str(e)}", level='WARNING')
                
                skipped_count += 1

        log_message(f"✅ Batch enqueue completed: 📈 {enqueued_count} enqueued, 📊 {skipped_count} skipped (already exist)", level='INFO')
        return enqueued_count, skipped_count

    def get_queue_status(self):
        """Get current queue status for debugging"""
        return {
            'queue_size': self.queue.qsize(),
            'active_workers': len(self.workers),
            'shutdown_requested': self.shutdown_event.is_set()
        }
    
    async def worker(self, worker_id):
        """Process queue items in batches"""
        log_message(f"Embedding worker {worker_id} started", level='INFO')

        while not self.shutdown_event.is_set():
            batch = []
            batch_items = []

            # Collect up to BATCH_SIZE items for a batch, but don't wait too long
            try:
                # Wait for at least one item, but with timeout
                item = await asyncio.wait_for(self.queue.get(), timeout=5.0)
                batch.append(item)
                batch_items.append(item)

                # Try to get more items quickly to fill the batch
                while len(batch) < BATCH_SIZE:
                    try:
                        item = await asyncio.wait_for(self.queue.get(), timeout=0.1)
                        batch.append(item)
                        batch_items.append(item)
                    except asyncio.TimeoutError:
                        break

            except asyncio.TimeoutError:
                # No items available, check if we should continue
                if self.shutdown_event.is_set():
                    break
                continue

            if batch:
                # log_message(f"Worker {worker_id} processing batch of {len(batch)} items", level='DEBUG')
                try:
                    async with self.semaphore:
                        await self.process_batch(batch)
                    # log_message(f"Worker {worker_id} completed batch of {len(batch)} items", level='DEBUG')
                except Exception as e:
                    log_message(f"Worker {worker_id} batch processing failed: {str(e)}", level='ERROR')
                finally:
                    # Mark all items in this batch as done
                    for _ in batch_items:
                        self.queue.task_done()

        log_message(f"Embedding worker {worker_id} stopped", level='INFO')

    async def start(self):
        """Start multiple processing workers"""
        if self.workers:
            log_message("Workers already started", level='WARNING')
            return

        # Create and start multiple workers
        for i in range(self.num_workers):
            worker_task = asyncio.create_task(self.worker(i))
            self.workers.append(worker_task)

        log_message(f"Started {self.num_workers} embedding queue workers", level='INFO')

    async def stop(self):
        """Stop all workers gracefully"""
        if not self.workers:
            return

        log_message("Stopping embedding queue workers...", level='INFO')

        # Signal shutdown
        self.shutdown_event.set()

        # Wait for workers to finish
        await asyncio.gather(*self.workers, return_exceptions=True)

        self.workers.clear()
        log_message("All embedding queue workers stopped", level='INFO')
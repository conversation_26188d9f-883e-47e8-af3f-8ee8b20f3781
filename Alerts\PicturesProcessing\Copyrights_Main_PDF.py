import os
import sys
sys.path.append(os.getcwd())
import os
import shutil
from logdata import log_message
from AI.GC_VertexAI import vertex_genai_multi_async
from AI.GCV_GetImageParts import get_image_parts
from AI.LLM_shared import get_json
from langfuse import observe
import langfuse
from Alerts.IPTrackingManager import IPTrackingManager # Added import
from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor # Added import for OCRProcessor
from typing import List
import Common.Constants as Constants

# VA == Visual Art
# TX == Computer File
# PA == Performance Art (Motion Picture)


@observe(capture_input=False, capture_output=False)
async def process_copyright_main_pdf(df, index, case_images_directory, pdf_path, reg_nos: List[str], ip_manager: IPTrackingManager):
    """
    Processes a PDF to find and extract copyright images for the given registration numbers.
    Updates the DataFrame and IPTrackingManager with found images.

    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        case_images_directory: Directory to save extracted images.
        pdf_path: Path to the PDF file to process.
        reg_nos: List of registration numbers to search for in the PDF.
        ip_manager: Instance of IPTrackingManager.
    
    Returns:
        List[str]: A list of registration numbers for which images were NOT found in this PDF.
    """
    if not pdf_path or not os.path.exists(pdf_path):
        log_message(f"PDF path is invalid or file does not exist: {pdf_path}", level="ERROR")
        return reg_nos # Return all reg_nos as not found if PDF is invalid

    log_message(f"Processing PDF {os.path.basename(pdf_path)} for {len(reg_nos)} copyright registration numbers.", level="INFO")
    
    # Initialize images and images_status if they don't exist or are not dicts
    if 'images' not in df.columns or not isinstance(df.at[index, 'images'], dict):
        df.at[index, 'images'] = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
    if 'copyrights' not in df.at[index, 'images'] or not isinstance(df.at[index, 'images']['copyrights'], dict):
        df.at[index, 'images']['copyrights'] = {}

    if 'images_status' not in df.columns or not isinstance(df.at[index, 'images_status'], dict):
        # A more robust initialization might be needed if this is the first time
        df.at[index, 'images_status'] = {'copyright_status': {'byregno': {'ai_reg_nos': [], 'count': 0}}}
    if 'copyright_status' not in df.at[index, 'images_status'] or not isinstance(df.at[index, 'images_status']['copyright_status'], dict):
        df.at[index, 'images_status']['copyright_status'] = {'byregno': {'ai_reg_nos': [], 'count': 0}}
    if 'byregno' not in df.at[index, 'images_status']['copyright_status'] or not isinstance(df.at[index, 'images_status']['copyright_status']['byregno'], dict):
        df.at[index, 'images_status']['copyright_status']['byregno'] = {'ai_reg_nos': [], 'count': 0}
    if 'ai_reg_nos' not in df.at[index, 'images_status']['copyright_status']['byregno']:
        df.at[index, 'images_status']['copyright_status']['byregno']['ai_reg_nos'] = []
    
    current_ai_nos = df.at[index, 'images_status']['copyright_status']['byregno'].get('ai_reg_nos', [])
    df.at[index, 'images_status']['copyright_status']['byregno']['ai_reg_nos'] = list(set(current_ai_nos + reg_nos))

    # Filter for valid "VA" numbers and those not already found
    valid_cr_nos = [str(reg_no) for reg_no in reg_nos if isinstance(reg_no, str) and reg_no.strip().upper().startswith("VA")]
    already_found_copyrights_details = df.at[index, 'images'].get("copyrights", {})
    already_found_reg_nos = set()
    if isinstance(already_found_copyrights_details, dict):
        for item_details in already_found_copyrights_details.values():
            if isinstance(item_details, dict) and 'reg_no' in item_details:
                reg_no_val = item_details['reg_no']
                if isinstance(reg_no_val, list):
                    already_found_reg_nos.update(reg_no_val)
                else:
                    already_found_reg_nos.add(str(reg_no_val))

    reg_nos_to_process_in_pdf = [reg_no for reg_no in valid_cr_nos if reg_no not in already_found_reg_nos]

    if not reg_nos_to_process_in_pdf:
        log_message(f"No new valid copyright registration numbers to process in {os.path.basename(pdf_path)}.", level="INFO")
        return []

    processed_count_in_pdf = 0
    reg_nos_not_found_in_this_pdf = []
    
    try:
        # Call to OCRProcessor.process_pdf: it returns (full_text, pages_text_json, page_ocr_data, page_images). We only need page_images for get_image_parts here.
        full_text, page_text, page_ocr_data, page_images = OCRProcessor.process_pdf(pdf_path)
    except Exception as e:
        log_message(f"Error during OCR processing of {pdf_path}: {e}", level="ERROR")
        return reg_nos_to_process_in_pdf

    prompt = """
    You are an expert in copyright registration numbers.
    You are given a list of copyright registration numbers and a document related to the court filings. For each number, you need to find the corresponding copyrighted picture in the document and return the page number of the picture.
    You return your answer in a JSON format with the following keys: {
    """
                                                                      
    for reg_no in reg_nos_to_process_in_pdf:
        prompt += f'"{reg_no}": page_number, '
    prompt += "}. Do not include REDACTED registration numbers in your answer. If a registration number has no associated copyrighted picture (we are looking for a picture of the artwork!!!), do not include the registration number in the JSON! Do not provide the page of the registration number if the picture of the artwork is not present."

    prompt_list = [("text", prompt), ("text", f"\n\nThis is pdf file: "), ("pdf_path", pdf_path)]

    ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.SMART_MODEL_FREE)
    ai_answer_json = get_json(ai_answer)

    reg_nos_llm_responded_for = set(ai_answer_json.keys()) if isinstance(ai_answer_json, dict) else set()

    for reg_no in reg_nos_to_process_in_pdf:
        if reg_no not in reg_nos_llm_responded_for:
            reg_nos_not_found_in_this_pdf.append(reg_no)
            continue

        item = ai_answer_json.get(reg_no)
        try:
            page_number = int(item)
            if page_number not in page_images:
                log_message(f"LLM returned invalid page number {page_number} for {reg_no} in {os.path.basename(pdf_path)}", level="WARNING")
                reg_nos_not_found_in_this_pdf.append(reg_no)
                continue
            image_page_content = page_images[page_number]
        except (ValueError, TypeError, KeyError):
            log_message(f"Error parsing LLM result or finding page for {reg_no} (item: {item}) in {os.path.basename(pdf_path)}", level="WARNING")
            reg_nos_not_found_in_this_pdf.append(reg_no)
            continue
        
        gcv_prompt = f"Identify a single (only 1!) bounding box for the Copyrighted Picture with registration number {reg_no}."
        parts_details = get_image_parts(gcv_prompt, image_page_content, image_format='webp')
        
        if not parts_details:
            log_message(f"🔥 No bounding box found by GCV for {reg_no} on page {page_number} in {os.path.basename(pdf_path)}", level="WARNING")
            reg_nos_not_found_in_this_pdf.append(reg_no)
            continue
        elif len(parts_details) > 1:
            log_message(f"🔥 More than 1 bounding box found by GCV for {reg_no} on page {page_number} in {os.path.basename(pdf_path)}. Taking the first one.", level="WARNING")
        
        copyright_image_path_from_gcv = parts_details[0]["path"]
        copyright_image_filename = os.path.basename(copyright_image_path_from_gcv)
        
        os.makedirs(case_images_directory, exist_ok=True)
        
        final_image_path = os.path.join(case_images_directory, copyright_image_filename)
        shutil.copy(copyright_image_path_from_gcv, final_image_path)
        
        copyright_image_full_filename = os.path.splitext(copyright_image_filename)[0] + "_full.webp"
        final_full_image_path = os.path.join(case_images_directory, copyright_image_full_filename)
        shutil.copy(final_image_path, final_full_image_path)
        
        if 'copyrights' not in df.at[index, 'images'] or not isinstance(df.at[index, 'images']['copyrights'], dict):
            df.at[index, 'images']['copyrights'] = {}
        df.at[index, 'images']['copyrights'][copyright_image_filename] = {'reg_no': [reg_no],'full_filename': [copyright_image_full_filename]}
        processed_count_in_pdf += 1

        location_id = f"pdf-{os.path.basename(pdf_path)}-regno-{reg_no}"
        ip_manager.record_finding('copyright', location_id, found_reg_nos=[reg_no])
        log_message(f"Successfully processed and saved image for {reg_no} from {os.path.basename(pdf_path)} page {page_number}.", level="INFO")

    if 'count' not in df.at[index, 'images_status']['copyright_status']['byregno']:
        df.at[index, 'images_status']['copyright_status']['byregno']['count'] = 0
    df.at[index, 'images_status']['copyright_status']['byregno']['count'] += processed_count_in_pdf
    
    langfuse.get_client().update_current_span(
        input={"NumRegNos": {len(reg_nos_to_process_in_pdf)}, "PDFPath": {os.path.basename(pdf_path)}},
        output={"LLMJsonForPages": {ai_answer_json if 'ai_answer_json' in locals() and ai_answer_json is not None else 'None/Error'}, "ImagesFoundViaLLMInPDF": {processed_count_in_pdf}, "RegNosNotProcessedFromPDF": {len(reg_nos_not_found_in_this_pdf)}, "CopyrightGoalMetAfter": {ip_manager.is_goal_met('copyright')}}
    )
    return reg_nos_not_found_in_this_pdf
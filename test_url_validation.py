#!/usr/bin/env python3
"""
Test script to verify URL validation error handling works correctly.
This script tests the complete error flow from URL validation to frontend display.
"""

import requests
import json
import time

# Test configuration
BASE_URL = "https://api.maidalv.com:5088"  # Adjust as needed
API_KEY = "37457-48774-8887-1882"  # Use a valid API key

def test_invalid_url_error():
    """Test that invalid URLs (missing extensions) are properly handled."""
    
    # Test data with invalid URL (missing image extension)
    test_data = {
        "api_key": API_KEY,
        "main_product_image": "https://au.triumph.com/cdn/shop/files/Pure-Invisible-Wired-Padded-Bra-Orange-10188657-61",  # Invalid - no extension
        "other_product_images": [],
        "ip_images": [],
        "ip_keywords": ["test"],
        "description": "Test product",
        "reference_text": "",
        "reference_images": [],
        "language": "en"
    }
    
    print("Testing invalid URL error handling...")
    print(f"Invalid URL: {test_data['main_product_image']}")
    
    # Submit the check request
    try:
        response = requests.post(f"{BASE_URL}/check_api", 
                               headers={"Content-Type": "application/json"},
                               json=test_data)
        
        if response.status_code != 200:
            print(f"❌ Initial request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
        result = response.json()
        check_id = result.get('check_id')
        
        if not check_id:
            print("❌ No check_id received")
            return False
            
        print(f"✅ Check submitted successfully. Check ID: {check_id}")
        
        # Poll for results
        max_polls = 20
        poll_count = 0
        
        while poll_count < max_polls:
            time.sleep(2)
            poll_count += 1
            
            status_response = requests.get(f"{BASE_URL}/check_status/{check_id}")
            
            if status_response.status_code != 200:
                print(f"❌ Status check failed with status {status_response.status_code}")
                continue
                
            status_result = status_response.json()
            print(f"Poll {poll_count}: Status = {status_result.get('status')}")
            
            if status_result.get('status') == 'error':
                # This is what we expect for invalid URL
                error_info = status_result.get('error', {})
                error_code = error_info.get('error_code')
                message = error_info.get('message')
                
                print(f"✅ Error status received as expected")
                print(f"   Error Code: {error_code}")
                print(f"   Message: {message}")
                
                # Check if we got the specific error code we expect
                if error_code == 'INVALID_URL_ERROR':
                    print("✅ Correct error code received: INVALID_URL_ERROR")
                    return True
                else:
                    print(f"❌ Expected INVALID_URL_ERROR, got: {error_code}")
                    return False
                    
            elif status_result.get('status') == 'completed':
                print("❌ Task completed unexpectedly (should have failed)")
                return False
                
            elif status_result.get('status') == 'processing':
                print("   Still processing...")
                continue
            else:
                print(f"❌ Unexpected status: {status_result.get('status')}")
                
        print("❌ Polling timed out")
        return False
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False

def test_valid_url():
    """Test that valid URLs work correctly (as a control test)."""
    
    test_data = {
        "api_key": API_KEY,
        "main_product_image": "https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png",  # Valid URL
        "other_product_images": [],
        "ip_images": [],
        "ip_keywords": ["test"],
        "description": "Test product",
        "reference_text": "",
        "reference_images": [],
        "language": "en"
    }
    
    print("\nTesting valid URL (control test)...")
    print(f"Valid URL: {test_data['main_product_image']}")
    
    try:
        response = requests.post(f"{BASE_URL}/check_api", 
                               headers={"Content-Type": "application/json"},
                               json=test_data)
        
        if response.status_code != 200:
            print(f"❌ Initial request failed with status {response.status_code}")
            return False
            
        result = response.json()
        check_id = result.get('check_id')
        
        print(f"✅ Valid URL test submitted. Check ID: {check_id}")
        print("   (Not waiting for completion - this is just to verify submission works)")
        return True
        
    except Exception as e:
        print(f"❌ Valid URL test failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("URL Validation Error Handling Test")
    print("=" * 60)
    
    # Test invalid URL
    invalid_test_passed = test_invalid_url_error()
    
    # Test valid URL as control
    valid_test_passed = test_valid_url()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS:")
    print(f"Invalid URL Error Test: {'✅ PASSED' if invalid_test_passed else '❌ FAILED'}")
    print(f"Valid URL Control Test: {'✅ PASSED' if valid_test_passed else '❌ FAILED'}")
    print("=" * 60)
    
    if invalid_test_passed:
        print("\n🎉 URL validation error handling is working correctly!")
        print("   - Invalid URLs are properly detected")
        print("   - Specific error codes are returned")
        print("   - Frontend should display user-friendly messages")
    else:
        print("\n❌ URL validation error handling needs attention")

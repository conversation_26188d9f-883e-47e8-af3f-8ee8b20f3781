// Results display and formatting functionality

// Function to display results in the output div
function displayResults(result, container) {
    const outputDiv = container || document.getElementById('output');
    const langEl = document.getElementById('language');
    const lang = langEl ? langEl.value : 'en';
    const isZh = lang === 'zh';

    // Clear previous content
    outputDiv.innerHTML = '';

    // Error handling is now centralized in check-form.js and this is no longer needed.

    // Create report container
    const reportContainer = document.createElement('div');

    // Check ID display
    const checkIdDisplay = document.createElement('p');
    checkIdDisplay.className = 'check-id';
    checkIdDisplay.innerHTML = `<strong data-i18n="check_id"></strong> ${result.check_id || 'N/A'}`;
    reportContainer.appendChild(checkIdDisplay);

    // Summary section
    const summaryDiv = document.createElement('div');
    summaryDiv.className = 'result-summary';
    summaryDiv.innerHTML = `
        <p><strong data-i18n="risk_level"></strong> ${result.risk_level}</p>
    `;
    reportContainer.appendChild(summaryDiv);

    // Results cards
    if (result.results && Array.isArray(result.results)) {
        const highRiskResults = result.results.filter(r => r.risk_level === 'High Risk' || r.risk_level === '高风险');
        const mediumRiskResults = result.results.filter(r => r.risk_level === 'Medium Risk' || r.risk_level === '中风险');
        const lowRiskResults = result.results.filter(r => r.risk_level === 'Low Risk' || r.risk_level === '低风险');

        let visibleResults = [];
        let hiddenResults = [];

        if (highRiskResults.length > 0) {
            visibleResults = highRiskResults.slice(0, 3);
            hiddenResults = highRiskResults.slice(3).concat(mediumRiskResults, lowRiskResults);
        } else if (mediumRiskResults.length > 0) {
            visibleResults = mediumRiskResults.slice(0, 1);
            hiddenResults = mediumRiskResults.slice(1).concat(lowRiskResults);
        } else if (lowRiskResults.length > 0) {
            visibleResults = lowRiskResults.slice(0, 1);
            hiddenResults = lowRiskResults.slice(1);
        }

        const allResults = visibleResults.concat(hiddenResults);

        allResults.forEach((res, index) => {
            const card = document.createElement('div');
            card.className = 'result-card';
            if (index >= visibleResults.length) {
                card.classList.add('hidden-result');
            }

            // Risk level fallback
            const riskLevel = res.risk_level || result.risk_level || 'N/A';

            // Report handling
            let reportHtml = '<p data-i18n="no_report"></p>';
            if (res.report) {
                reportHtml = `
                    <button type="button" class="show-report-button" data-report-id="report-${index}">
                        ${isZh ? i18n.zh.show_report : i18n.en.show_report}
                    </button>
                    <div id="report-${index}" class="report-container" style="display: none;">
                        <div class="report-text">
                            ${formatReportText(res.report)}
                        </div>
                    </div>
                `;
            }

            // IP images handling
            let ipImagesHtml = '';
            if (res.ip_asset_urls) {
                if (typeof res.ip_asset_urls === 'string' && res.ip_asset_urls.startsWith('[')) {
                    try {
                        res.ip_asset_urls = JSON.parse(res.ip_asset_urls);
                    } catch (e) { console.error("Failed to parse ip_asset_urls", e); }
                } else if (typeof res.ip_asset_urls === 'string') {
                    res.ip_asset_urls = [res.ip_asset_urls];
                }
                if (Array.isArray(res.ip_asset_urls)) {
                    ipImagesHtml = res.ip_asset_urls.map(filename => `<img src="${filename}" class="uniform-img">`).join('');
                }
            }

            // Product images handling
            let productImagesHtml = '';
            if (res.product_url) {
                if (typeof res.product_url === 'string' && res.product_url.startsWith('[')) {
                    try {
                        res.product_url = JSON.parse(res.product_url);
                    } catch(e) { console.error("Failed to parse product_url", e); }
                } else if (typeof res.product_url === 'string') {
                    res.product_url = [res.product_url];
                }
                if (Array.isArray(res.product_url)) {
                    productImagesHtml = res.product_url.map(url =>`<img src="${url}" class="uniform-img">`).join('');
                }
            }

            let ownerDetailsHtml = '';
            if (res.ip_owner && res.ip_owner !== '[]') {
                ownerDetailsHtml += `<p><strong data-i18n="ip_owner"></strong> ${res.ip_owner}</p>`;
            }
            if (res.text) {
                const textI18nKey = (res.ip_type === 'Patent' || res.ip_type === 'patent') ? 'patent_title' : 'trademark_text';
                ownerDetailsHtml += `<p><strong data-i18n="${textI18nKey}"></strong> ${res.text}</p>`;
            }
            if (res.reg_no) {
                ownerDetailsHtml += `<p><strong data-i18n="registration_number"></strong> ${res.reg_no}</p>`;
            }
            if (res.ser_no) {
                ownerDetailsHtml += `<p><strong data-i18n="serial_number"></strong> ${res.ser_no}</p>`;
            }

            let troDetailsHtml = '';
            if (res.plaintiff_name) {
                troDetailsHtml = `
                    <p><strong data-i18n="used_in_tro">Has been used in TRO:</strong> Yes</p>
                    <p><strong data-i18n="last_case_docket">Last Docket number:</strong> ${res.last_case_docket || 'N/A'}</p>
                    <p><strong data-i18n="plaintiff_name">Plaintiff Name:</strong> ${res.plaintiff_name}</p>
                    <p><strong data-i18n="number_of_cases">Number of cases:</strong> ${res.number_of_cases}</p>
                `;
            } else {
                troDetailsHtml = `<p><strong data-i18n="used_in_tro">Has been used in TRO:</strong> No</p>`;
            }

            card.innerHTML = `
                <h3>${isZh ? '结果' : 'Result'} ${index + 1}</h3>
                <p><strong data-i18n="type"></strong> ${res.ip_type}</p>
                ${ownerDetailsHtml}
                <p><strong data-i18n="risk_level"></strong> ${riskLevel}</p>
                <p><strong data-i18n="risk_description"></strong> ${res.risk_description}</p>
                ${troDetailsHtml}
                ${ipImagesHtml ? `<p><strong data-i18n="ip_image"></strong><br>${ipImagesHtml}</p>` : ''}
                ${productImagesHtml ? `<p><strong data-i18n="product_image"></strong><br>${productImagesHtml}</p>` : ''}
                ${reportHtml}
            `;

            reportContainer.appendChild(card);
        });

        if (hiddenResults.length > 0) {
            const showMoreBtn = document.createElement('button');
            showMoreBtn.id = 'show-more-btn';
            showMoreBtn.dataset.i18n = 'show_more_results';
            showMoreBtn.textContent = isZh ? i18n.zh.show_more_results : i18n.en.show_more_results;
            reportContainer.appendChild(showMoreBtn);

            showMoreBtn.addEventListener('click', () => {
                document.querySelectorAll('.hidden-result').forEach(el => {
                    el.style.display = 'block';
                });
                showMoreBtn.style.display = 'none';
            });
        }
    }

    outputDiv.appendChild(reportContainer);

    // Update translations for dynamic content
    document.querySelectorAll('[data-i18n]').forEach(el => {
        const key = el.dataset.i18n;
        const lang = document.getElementById('language')?.value || 'en';
        if (i18n[lang] && i18n[lang][key]) {
            el.textContent = i18n[lang][key];
        }
    });
}

// Export functions for global access
window.displayResults = displayResults;

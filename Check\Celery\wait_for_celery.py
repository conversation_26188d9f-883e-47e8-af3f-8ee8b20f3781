import time
from celery_app import celery

def wait_for_workers(timeout=180):
    """
    Waits for at least one Celery worker to be available.
    Pings workers every 2 seconds until a reply is received or timeout is reached.
    """
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            # The inspect().ping() method sends a ping to all workers.
            # It returns a dictionary with replies from online workers.
            i = celery.control.inspect()
            ping_response = i.ping()
            
            if ping_response:
                print(f"✅ Celery worker(s) are up and running: {list(ping_response.keys())}")
                return True
        except Exception as e:
            # This can happen if the broker is not yet available.
            print(f"🟠 Waiting for Celery worker... (Error: {e})")
        
        time.sleep(2)

    print("❌ Timed out waiting for Celery workers to become available.")
    return False

if __name__ == "__main__":
    print("🔎 Checking for active Celery workers...")
    if wait_for_workers():
        exit(0)
    else:
        exit(1)
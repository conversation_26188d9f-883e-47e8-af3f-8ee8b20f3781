# 0. Setup a internal non raid SSD:

fdisk -l # to find out the device name (nvmeXn1)
sudo parted /dev/nvmeXn1 mklabel gpt
sudo parted -a optimal /dev/nvmeXn1 mkpart primary ext4 0% 100%
sudo mkfs.ext4 /dev/nvmeXn1p1
sudo mkdir /mnt/4tb
sudo blkid /dev/nvmeXn1p1 # To find the UUID
sudo nano /etc/fstab # To add: UUID=YOUR_UUID_HERE /mnt/4tb ext4 defaults 0 2
sudo mount -a
sudo chmod 777 /mnt/4tb
sudo systemctl daemon-reload

# 1. Install docker

sudo apt-get update
sudo apt-get install ca-certificates curl
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

# Add the repository to Apt sources:

echo 
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu 
  $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | 
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update

sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# 2. Prepare folders

sudo mkdir -p /docker/qdrant_storage
sudo chmod 777 /docker/qdrant_storage
sudo mkdir -p /mnt/4tb/qdrant/snapshots
sudo chmod 777 /mnt/4tb/qdrant/snapshots
sudo mkdir -p /mnt/4tb/qdrant/temp
sudo chmod 777 /mnt/4tb/qdrant/temp
sudo mkdir -p /docker/postgresql
sudo chmod 777 /docker/postgresql
sudo mkdir -p /docker/prometheus
sudo chmod 777 /docker/prometheus
sudo mkdir -p /docker/grafana
sudo chmod 777 /docker/grafana

sudo mkdir -p /docker/loki/chunks /docker/loki/tsdb-index /docker/loki/tsdb-cache
sudo chown -R 10001:10001 /docker/loki

# 6. Add cloudflared:

sudo mkdir -p --mode=0755 /usr/share/keyrings
curl -fsSL https://pkg.cloudflare.com/cloudflare-main.gpg | sudo tee /usr/share/keyrings/cloudflare-main.gpg >/dev/null
echo 'deb [signed-by=/usr/share/keyrings/cloudflare-main.gpg] https://pkg.cloudflare.com/cloudflared any main' | sudo tee /etc/apt/sources.list.d/cloudflared.list
sudo apt-get update && sudo apt-get install cloudflared

sudo cloudflared service install eyJhIjoiYzA3NWM0MzIxMjJkZjliY2ZmMjJkZGVmMzlkZjAxNzAiLCJ0IjoiNDE0ZWFjNWMtMWNkOC00NTg0LWIwMTctZGRiNmIxYTEzZjE5IiwicyI6Ik5qTXhNMlE1TWpFdFpEWXpZaTAwT1RnMUxXSmhabVF0TmpJNU5UVTJabUl5T0RNeiJ9

# 7. Start and test

docker compose up -d

Verify:
Check running containers: docker ps
Check logs if needed: docker-compose logs -f <service_name> (e.g., docker-compose logs -f prometheus)
Access UIs:
Prometheus: http://<your_server_ip_or_domain>:9090 (Check Status -> Targets to see if all jobs are scraping successfully - they should be 'UP').
Grafana: http://<your_server_ip_or_domain>:3000 (Login with user admin and the password you set).
cAdvisor (Optional): http://<your_server_ip_or_domain>:8080

# 8. Configure Grafana:

Add Data Source: In Grafana, go to Configuration (Gear icon) -> Data Sources -> Add data source. Select Prometheus. Set the URL to http://prometheus:9090. Click Save & Test.
Import Dashboards: Go to Dashboards (Four squares icon) -> Browse -> Import.
Qdrant: Use dashboard ID 23033. Select your Prometheus data source.
PostgreSQL: Use a suitable ID like 9628 (PostgreSQL Database). Select your Prometheus data source. Adjust instance labels if needed based on the dashboard's design.
Node Exporter / Host: Use a suitable ID like 1860 (Node Exporter Full). Select your Prometheus data source.
Docker/cAdvisor: Search for dashboards using "cAdvisor" or "Docker Host/Container". ID 193 (Docker Host/Container Overview) or similar might work. Select your Prometheus data source.

# 9. uuid-ossp Extension: Confirmation that the uuid-ossp PostgreSQL extension is enabled in the target database, or instructions/permission to enable it (needed for uuid_generate_v4()).

# 10. Setup Alerting (related with prometheus, e.g. email or whatsapp)

# 11. Firwall: open port 5432 and 22

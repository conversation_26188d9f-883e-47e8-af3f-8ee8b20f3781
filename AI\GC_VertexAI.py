# Get started: in google cloud, create a new project and enable Vertex AI API
# Poweshell: gcloud components update
# Poweshell: gcloud auth application-default login
# Poweshell: pip3 install --upgrade "google-cloud-aiplatform>=1.64"

# Location: 
# flash-2.0-exp is only available in Iowa (us-central1): Joke: 1.3 sec from Oracle Ashburn, 1.4 sec from Hetzner Germany, 4 sec from Bishkek

import time, os, sys, base64, json, cv2, asyncio, pathlib, multiprocessing
sys.path.append(os.getcwd())
start_time = time.time()
import Common.Constants as Constants
from google.genai import Client
if multiprocessing.current_process().name == 'MainProcess':
    print(f"                     Vertex: 'from google.genai import Client' after {time.time()-start_time:.2f} seconds")  # 10 sec
from google.genai.types import Tool, GenerateContentConfig, GoogleSearch, SafetySetting, Image, Part, Blob, ThinkingConfig
# from google.genai.types import Image
# import PIL, tempfile, mimetypes
start_time = time.time()
from langfuse import observe
import langfuse
if multiprocessing.current_process().name == 'MainProcess':
    print(f"                     Vertex: 'import langfuse' after {time.time()-start_time:.2f} seconds")
# from langfuse.media import LangfuseMedia
# import numpy as np
start_time = time.time()
from FileManagement.GC_COS import upload_to_gcs, delete_from_gcs
if multiprocessing.current_process().name == 'MainProcess':
    print(f"                     Vertex: 'import FileManagement.GC_COS' after {time.time()-start_time:.2f} seconds")  # 2 sec
from AI.GC_Credentials import get_gcs_client_multi_project, get_gcs_credentials, get_gemini_api_key
from AI.LLM_shared import get_json, get_json_list

# Models supported: https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/inference#python
# Experimental models free: https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/gemini-experimental#rest
# Gemini Experimental: 10 request / minute
# Llama 3.2: 30 request / minute

# Free image generation with imagen 3: https://console.cloud.google.com/vertex-ai/studio/vision?inv=1&invt=AbmAcQ&project=trodata

# Langfuse for VertexAI: https://langfuse.com/docs/integrations/google-vertex-ai

# SDK doc: https://googleapis.github.io/python-genai/

# Test bounding box: https://langtail.com/gemini-bounding-boxes

# Model Names: gemini-2.0-flash-lite-preview-02-05, gemini-2.5-pro-exp-03-25, gemini-2.5-pro-exp-03-25, gemini-2.0-flash, gemini-exp-1206, gemini-2.0-flash-exp


# ❌⚠️📥🔥✅

safety_config = [
    SafetySetting(
        category="HARM_CATEGORY_DANGEROUS_CONTENT",
        threshold="BLOCK_NONE",
    ),
    SafetySetting(
        category="HARM_CATEGORY_HARASSMENT",
        threshold="BLOCK_NONE",
    ),
    SafetySetting(
        category="HARM_CATEGORY_SEXUALLY_EXPLICIT",
        threshold="BLOCK_NONE",
    ),
    SafetySetting(
        category="HARM_CATEGORY_HATE_SPEECH",
        threshold="BLOCK_NONE",
    )
]

mime_types = {".jpg": "image/jpeg",".jpeg": "image/jpeg",".png": "image/png",".webp": "image/webp"}

models_thinking_budget = ["gemini-2.5-flash", "gemini-2.5-flash-lite-preview-06-17"]

## Sync functions

def llm_call(prompt, model_name=Constants.TEXT_MODEL_FREE, max_retries=3, delay=60, useVertexAI=True):
    return asyncio.run(vertex_genai_multi_async(([("text", prompt)], None, model_name, max_retries, delay, useVertexAI)))

def vertex_genai_text(prompt, model_name=Constants.TEXT_MODEL_FREE, max_retries=3, delay=60, useVertexAI=True):
    return asyncio.run(vertex_genai_multi_async([("text", prompt)], None, model_name, max_retries, delay, useVertexAI))

async def vertex_genai_text_async(prompt, model_name=Constants.TEXT_MODEL_FREE, max_retries=3, delay=60, useVertexAI=True):
    return await vertex_genai_multi_async([("text", prompt)], None, model_name, max_retries, delay, useVertexAI)
    
def vertex_genai_image(prompt, image_path, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=35, useVertexAI=True):
    return asyncio.run(vertex_genai_image_async(prompt, image_path, image_url, model_name, max_retries, delay, useVertexAI))

async def vertex_genai_image_async(prompt, image_path, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=35, useVertexAI=True):
    return await vertex_genai_multi_async([("text", prompt), ("image_path", image_path)], image_url, model_name, max_retries, delay, useVertexAI)

def vertex_genai_multi(data_list, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=35, useVertexAI=True):
    return asyncio.run(vertex_genai_multi_async(data_list, image_url, model_name, max_retries, delay, useVertexAI))

def vertex_genai_bounding_box(data_list, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=60):
    return asyncio.run(vertex_genai_bounding_box_async(data_list, image_url, model_name, max_retries, delay))

def vertex_genai_search(data_list, image_url=None, model_name=Constants.TEXT_MODEL_FREE, max_retries=3, delay=60):
    return asyncio.run(vertex_genai_search_async(data_list, image_url, model_name, max_retries, delay))

def vertex_genai_image_gen(data_list, image_url=None, model_name=Constants.IMAGE_GEN_MODEL_FREE
, max_retries=3, delay=60):
    return asyncio.run(vertex_genai_image_gen_async(data_list, image_url, model_name, max_retries, delay))


## Async functions with @observe

@observe(as_type="generation")
async def vertex_genai_multi_async(data_list, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=35, useVertexAI=True):
    def handle_response(response):
        return response.text if response.text else None
    
    return await _vertex_genai_common_async(
        data_list=data_list,
        image_url=image_url,
        model_name=model_name,
        max_retries=max_retries,
        delay=delay,
        useVertexAI=useVertexAI,
        config=GenerateContentConfig(safety_settings=safety_config,temperature=0),
        response_handler=handle_response
    )


@observe(as_type="generation")
async def vertex_genai_bounding_box_async(data_list, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=35, useVertexAI=True):
    
    def handle_response_no_dupe(response):
        if response.text:
            # Remove duplicate bounding boxes
            try:
                if "[" in response.text and response.text.find("[") < response.text.find("{"):
                    try: 
                        response_list = json.loads(parse_json(response.text))
                    except:
                        response_list = get_json_list(response.text)
                else:
                    response_list = [get_json(response.text)]
                return [item for index, item in enumerate(response_list) if item not in response_list[:index]]
            except Exception as e:
                if "unable to" in response.text.lower() or "helpful" in response.text.lower() or "limited" in response.text.lower():
                    print(f"Bounding box not found. Response: {response.text}")
                else:
                    print(f"\033[91mError parsing bounding box response: {response.text}\033[0m")
                return None
        return None

    return await _vertex_genai_common_async(
        data_list=data_list,
        image_url=image_url,
        model_name=model_name,
        max_retries=max_retries,
        delay=delay,
        useVertexAI=useVertexAI,
        config=GenerateContentConfig(
            safety_settings=safety_config,
            system_instruction="Return bounding boxes as a JSON array with labels. Never return masks or code fencing. Limit to 25 objects.",
            temperature=0, # Reducing the temperature leads to less results
        ),
        response_handler=handle_response_no_dupe
    )


@observe(as_type="generation")
async def vertex_genai_bbox_mask_async(data_list, image_url=None, model_name=Constants.IMAGE_MODEL_FREE, max_retries=3, delay=35, useVertexAI=True, include_masks: bool = False):
    """
    Calls Vertex AI/Gemini to get bounding boxes and optionally segmentation masks.
    Uses different system prompts based on whether masks are requested.
    """
    def handle_response_bbox_mask(response):
        """Handles JSON response, parsing, and removing duplicates."""
        if response.text:
            try:
                # Use the existing parse_json helper to remove potential markdown fencing
                parsed_text = parse_json(response.text)
                response_list = json.loads(parsed_text)
                # Duplicate removal logic removed to prevent hashing errors with lists
                return response_list # Return the raw list directly
            except Exception as e:
                # Basic error handling for non-JSON or refusal responses
                if "unable to" in response.text.lower() or "helpful" in response.text.lower() or "limited" in response.text.lower():
                    print(f"Object detection/segmentation failed. Response: {response.text}")
                else: # Genuine parsing error
                    print(f"\033[91mError parsing object detection/segmentation response: {response.text}\nError: {e}\033[0m")
                return [] # Return empty list on handled errors/refusals
        return [] # Return empty list if response.text is initially empty

    # Determine the system instruction based on include_masks
    if include_masks:
        system_instruction = "Give the segmentation masks for the objects. Output a JSON list of segmentation masks where each entry contains the 2D bounding box in \"box_2d\" and the mask in \"mask\"."
        system_instruction = "Give the segmentation masks for the requested parts. Output a JSON list of segmentation masks where each entry contains the 2D bounding box in the key \"box_2d\", the segmentation mask in key \"mask\", and the text label in the key \"label\". Use descriptive labels."
    else:
        system_instruction = "Return bounding boxes as a JSON array with labels. Never return masks or code fencing. Limit to 25 objects."

    return await _vertex_genai_common_async(
        data_list=data_list,
        image_url=image_url,
        model_name=model_name,
        max_retries=max_retries,
        delay=delay,
        useVertexAI=useVertexAI,
        config=GenerateContentConfig(
            safety_settings=safety_config,
            system_instruction=system_instruction,
            temperature=0, # Keep temperature at 0 for consistency
        ),
        response_handler=handle_response_bbox_mask # Use the refined handler
    )

@observe(as_type="generation")
async def vertex_genai_search_async(data_list, image_url=None, model_name=Constants.TEXT_MODEL_FREE, max_retries=3, delay=60):
    
    def handle_response(response):
        # for each in response.candidates[0].content.parts:
        #     print(f"LLM Search Result: {each.text}")
        return response.candidates[0].content.parts[0].text if response.candidates[0].content.parts else None

    return await _vertex_genai_common_async(
        data_list=data_list,
        image_url=image_url,
        model_name=model_name,
        max_retries=max_retries,
        delay=delay,
        useVertexAI=True,  # Search requires Vertex AI credentials
        config=GenerateContentConfig(safety_settings=safety_config, temperature=0, tools=[Tool(google_search=GoogleSearch())], response_modalities=["TEXT"]),
        response_handler=handle_response
    )

@observe(as_type="generation")
async def vertex_genai_image_gen_async(data_list, image_url=None, model_name=Constants.IMAGE_GEN_MODEL_FREE, max_retries=3, delay=60):
# Only returns the first part of the response. Assumes the model output only 1 part.
    
    def handle_response(response):
        # Check for non-streaming response structure first
        if response.candidates and response.candidates[0].content and response.candidates[0].content.parts:
            part = response.candidates[0].content.parts[0]
            if part.inline_data:
                return part.inline_data
            elif part.text: # Handle cases where text might be returned instead of an image (e.g., safety filter)
                print(f"Received text response instead of image: {part.text}")
                return part.text 

        print("⚠️ No image data or text found in response.")
        return None

    return await _vertex_genai_common_async(
        data_list=data_list, image_url=image_url, model_name=model_name,
        max_retries=max_retries, delay=delay, useVertexAI=True,
        config=GenerateContentConfig(temperature=0, response_modalities=["IMAGE", "TEXT"]),  # image generation model  uses a different non-configurable set of safety filters
        response_handler=handle_response
    )


## Async function called by every other functions
async def _vertex_genai_common_async(data_list, image_url, model_name, max_retries, delay, useVertexAI, config, response_handler=None, thinking_budget=0):
    # List to store cloud PDF blob information that should be deleted after processing
    pdf_blobs = []
    pdf_files_api = []

    for attempt in range(max_retries):
        try:
            start_time = time.time()
            langfuse_prompt = ""
            my_contents = []

            for i, (data_type, data_value) in enumerate(data_list):
                if data_type == "text":
                    langfuse_prompt += data_value
                    my_contents.append(Part.from_text(text=data_value))
                
                elif data_type == "image_path":
                    if image_url:
                        langfuse_prompt += f"\n![Alt text]({image_url[i]})"
                    else: 
                        langfuse_prompt += f"\nImage: {os.path.basename(data_value)}"
                    
                    mime_type = mime_types.get(os.path.splitext(data_value)[1].lower(), "image/jpeg")

                    with open(data_value, 'rb') as image_file:
                        data_bytes = image_file.read()
                    
                    if not data_bytes:
                        print(f"⚠️⚠️⚠️ Failed to read image file: {data_value}")
                    
                    my_contents.append(Part.from_bytes(data=data_bytes, mime_type=mime_type))
                
                elif data_type == "image_pil":
                    if image_url:
                        langfuse_prompt += f"\n![Alt text]({image_url[i]})"
                    else: 
                        langfuse_prompt += f"\n===Image in PIL format==="
                    my_contents.append(data_value)
                
                elif data_type == "image_cv2":
                    _, encoded_image = cv2.imencode(".jpg", data_value)
                    image_bytes = encoded_image.tobytes()
                    if image_url:
                        langfuse_prompt += f"\n![Alt text]({image_url[i]})"
                    else: 
                        langfuse_prompt += f"\n===Image in cv2 format==="
                    my_contents.append(Part.from_bytes(data=image_bytes, mime_type="image/jpeg"))
                
                elif data_type == "pdf_path":  # Handle PDFs up to 50MB (Gemini API: <20MB direct, 20-50MB File API; Vertex AI: cloud storage for large files)

                    file_size = os.path.getsize(data_value)

                    if file_size > 48000000:  # 50MB limit for Gemini API
                        if useVertexAI:
                            # For files > 50MB, use Vertex AI with Google Cloud Storage
                            langfuse_prompt += f"\nPDF uploaded to google cloud: {data_value}"
                            blob_location = f"genai_data/{os.path.basename(data_value)}"
                            upload_to_gcs(data_value, "trodata_bucket", blob_location)
                            pdf_blobs.append(("trodata_bucket", blob_location))
                            my_contents.append(Part.from_uri(file_uri=f"gs://trodata_bucket/{blob_location}", mime_type="application/pdf"))
                        else:
                            print(f"❌ PDF size ({file_size/1000000:.1f}MB) exceeds Gemini API 50MB limit. Use useVertexAI=True for files larger than 50MB: {data_value}")
                            continue
                    elif file_size < 18000000:  # Files under 20MB can be uploaded directly via Part.from_bytes
                        langfuse_prompt += f"\nPDF passed as data (read_bytes): {data_value}"
                        filepath = pathlib.Path(data_value)
                        my_contents.append(Part.from_bytes(data=filepath.read_bytes(),mime_type='application/pdf'))
                    else:  # Files 20MB-50MB
                        if useVertexAI:
                            # For 20MB-50MB files with Vertex AI, use cloud storage for better reliability
                            langfuse_prompt += f"\nPDF uploaded to google cloud: {data_value}"
                            blob_location = f"genai_data/{os.path.basename(data_value)}"
                            upload_to_gcs(data_value, "trodata_bucket", blob_location)
                            pdf_blobs.append(("trodata_bucket", blob_location))
                            my_contents.append(Part.from_uri(file_uri=f"gs://trodata_bucket/{blob_location}", mime_type="application/pdf"))
                        else:
                            # For Gemini API 20MB-50MB files, use File API
                            langfuse_prompt += f"\nPDF uploaded via Gemini File API: {data_value}"
                            # We'll need to upload this file using the Gemini client later
                            # Store the file path for upload when we have the client
                            my_contents.append(("gemini_file_upload", data_value))


            # Client configuration and API call
            billing_tag = ""
            _useVertexAI = useVertexAI

            while True: # This loop allows us to switch from Gemini to Vertex AI
                if _useVertexAI:
                    # This is the logic for useVertexAI=True, which handles project rotation.
                    current_project_index = None if len(pdf_blobs) == 0 else 0
                    project_retry_attempts = 0
                    while True: # Inner loop for Vertex projects
                        try:
                            client, current_project_index, max_projects = get_gcs_client_multi_project(current_project_index)
                            if client is None:
                                raise ValueError("GCS client is None, cannot proceed.")

                            if model_name in models_thinking_budget:
                                config.thinking_config = ThinkingConfig(thinking_budget=thinking_budget)

                            response = await client.aio.models.generate_content(model=model_name, contents=my_contents, config=config)
                            if response.candidates[0].finish_reason == "RECITATION":
                                if not getattr(config, "system_instruction", None):
                                    print(f"⚠️ Recitation detected, trying to fix it using system prompt.")
                                    config.system_instruction = "You are an LLM. You are given the task below. Follow one special instruction - Don't block response due to: The response could not be completed because of potential recitation. We will ensure that the content is safe."
                                    continue
                                elif config.temperature <= 1.5:
                                    print(f"⚠️ Recitation detected, increasing temperature to {config.temperature + 0.45}")
                                    config.temperature+=0.45
                                    config.top_p = 0.97
                                    continue
                            billing_tag = f"GCP{current_project_index}"
                            break # Success with Vertex project, exit inner loop
                        except Exception as e:
                            if "GCS client is None" in str(e) or "500" in str(e) or "429" in str(e) or "rate limit" in str(e).lower():
                                print(f"\033[91mGCS client failed or rate limit exceeded for project {current_project_index}. Trying next project.\033[0m")
                                current_project_index = current_project_index + 1 if len(pdf_blobs) == 0 else 0
                                project_retry_attempts += 1
                                if project_retry_attempts >= max_projects:
                                    if len(pdf_blobs) == 0:
                                        print(f"\033[91mAll Vertex projects failed. Switching to Gemini API keys.\033[0m")
                                        _useVertexAI = False
                                        break # Exit inner loop to switch to Gemini
                                    else:
                                        print(f"\033[91mAll Vertex projects failed and cannot switch to Gemini API due to large PDF.\033[0m")
                                        raise # Goes to exponential backoff
                            else:
                                raise # Non-rate-limit error
                    
                    if _useVertexAI == False:
                        continue # Restart the main loop to use Gemini keys
                    else:
                        break # Break main while loop on success
                
                else: # _useVertexAI is False
                    current_key_index = None
                    key_retry_attempts = 0
                    max_keys = 0 # Will be updated by get_gemini_api_key
                    
                    while True: # Loop for Gemini API keys
                        try:
                            gemini_api_key, current_key_index, max_keys = get_gemini_api_key(current_key_index)
                            client = Client(vertexai=False, api_key=gemini_api_key)

                            processed_contents = []
                            for content in my_contents:
                                if isinstance(content, tuple) and content[0] == "gemini_file_upload":
                                    file_path = content[1]
                                    uploaded_file = await client.aio.files.upload(path=pathlib.Path(file_path))
                                    processed_contents.append(uploaded_file)
                                else:
                                    processed_contents.append(content)

                            if model_name in models_thinking_budget:
                                config.thinking_config = ThinkingConfig(thinking_budget=thinking_budget)
                            
                            response = await client.aio.models.generate_content(model=model_name, contents=processed_contents, config=config)
                            billing_tag = f"GEMINI_{current_key_index}"
                            break # Success with Gemini key

                        except Exception as e:
                            if "429" in str(e) or "rate limit" in str(e).lower():
                                print(f"\033[91mRate limit on Gemini API key {current_key_index}. Trying next key.\033[0m")
                                current_key_index = (current_key_index + 1) if current_key_index is not None else 1
                                key_retry_attempts += 1
                                if key_retry_attempts >= max_keys:
                                    print(f"\033[91mAll Gemini API keys failed. Switching to useVertexAI=True.\033[0m")
                                    _useVertexAI = True
                                    break # Break from this inner loop to switch to Vertex
                            else:
                                raise # Raise other errors to the main retry handler
                    
                    if _useVertexAI == True:
                        continue # Re-run the main `while` loop, which will now use the `if _useVertexAI:` branch
                    else:
                        break # Success with a Gemini key, so break the main `while` loop

            # Langfuse logging (with system instruction handling difference)
            langfuse_input = f"{getattr(config, 'system_instruction', '') or ''}\n\n{langfuse_prompt}"  # the or '' is to handle the case where system_instruction is there but equal None
            
            # Check if the response contains image data (gen ai image) before accessing .text
            raw_answer_for_logging = "N/A"
            if response.candidates and response.candidates[0].content and response.candidates[0].content.parts:
                part = response.candidates[0].content.parts[0]
                if part.inline_data:
                    raw_answer_for_logging = f"Received image data (mime_type: {part.inline_data.mime_type})"
                elif part.text:
                    raw_answer_for_logging = part.text
            
            langfuse.get_client().update_current_generation(
                input=langfuse_input,
                model=model_name,
                metadata={"tags": [billing_tag], "raw_answer": raw_answer_for_logging },
                usage_details={
                    "input": response.usage_metadata.prompt_token_count,
                    "output": response.usage_metadata.candidates_token_count
                }
            )

            # Delete PDF blob(s) from GCS after receiving the LLM response
            for bucket, blob_location in pdf_blobs:
                try:
                    delete_from_gcs(bucket, blob_location)
                except Exception as del_error:
                    print(f"Failed to delete blob {blob_location} from bucket {bucket}: {del_error}")
                    
            # Delete PDF file(s) from Gemini API after receiving the LLM response: not needed, it is auto deleted 48h later

            if response_handler:
                return response_handler(response)
            else:
                return response

        except Exception as e:
            if attempt < max_retries - 1:
                print(f"_vertex_genai_common_async with prompt [{langfuse_prompt[:50]}...] call failed (attempt {attempt + 1}/{max_retries}): {e}")
                elapsed_time = time.time() - start_time
                remaining_delay = max(0, ((attempt + 1) * delay) - elapsed_time)
                print(f"Waiting {remaining_delay:.1f} seconds before retrying...")
                await asyncio.sleep(remaining_delay)
                start_time = time.time()
            else:
                print(f"_vertex_genai_common_async with prompt [{langfuse_prompt[:50]}...] call failed after {max_retries} attempts: {e}")
                raise



def encode_file_uri(image_path):
    # Get the file extension and map it to MIME type
    mime_type = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif'
    }.get(os.path.splitext(image_path)[1].lower(), 'application/octet-stream')
    
    # Read and encode the file
    with open(image_path, "rb") as file:
        base64_content = base64.b64encode(file.read()).decode("utf-8")
        return f"data:{mime_type};base64,{base64_content}"


def parse_json(json_output): # Provided by Google
    # Parsing out the markdown fencing
    lines = json_output.splitlines()
    for i, line in enumerate(lines):
        if line == "```json":
            json_output = "\n".join(lines[i+1:])  # Remove everything before "```json"
            json_output = json_output.split("```")[0]  # Remove everything after the closing "```"
            break  # Exit the loop once "```json" is found
    return json_output


@observe()
def tell_a_joke():
    print(vertex_genai_text("Tell me a joke"))
    
    
def test_model_names():
    
    my_contents = [Part.from_text(text="Tell me a joke")]
    client, current_project_index, max_projects = get_gcs_client_multi_project(None)

    print(client.models.generate_content(model="gemini-2.0-flash-exp", contents=my_contents).text)
    print("done")
    
def test_model_names_google():
    my_contents = [Part.from_text(text="Tell me a joke")]
    
    client = Client(vertexai=False, api_key=os.environ["GEMINI_API_KEY_JG"])
    print(client.models.generate_content(model="gemini-2.5-pro-preview-05-06", contents=my_contents).text)
    print("done")    
    
                        


def test_pdf_google():
    import pathlib
    
    local_file_path = "D:/Documents/Programing/TRO/USside/Documents/Case Files/2023-04-18 - 1_23-cv-02421/1/IN_DC_1_23-cv-02421_2023-04-18_1_COMPLAINT_filed_by_Princess_Karibo_Filing_fee_402_.pdf"
    filepath = pathlib.Path(local_file_path)

    client = Client(vertexai=False, api_key=os.environ["GEMINI_API_KEY"])
    # file = client.files.upload(path=local_file_path)

    doc_data = Part.from_bytes(
        data=filepath.read_bytes(),  # Read bytes from the local file
        mime_type='application/pdf',
      ),
    
    response = client.models.generate_content(
        model='gemini-2.0-flash-exp',
        contents=['Could you summarize this file?', doc_data]
    )
    print(response.text)

def test_pdf_google_uri():
    import pathlib
    
    local_file_path = "D:/Documents/Programing/TRO/USside/Documents/Case Files/2023-04-18 - 1_23-cv-02421/1/IN_DC_1_23-cv-02421_2023-04-18_1_COMPLAINT_filed_by_Princess_Karibo_Filing_fee_402_.pdf"
    filepath = pathlib.Path(local_file_path)

    client = Client(vertexai=False, api_key=os.environ["GEMINI_API_KEY"])
    file = client.files.upload(path=filepath)
    
    response = client.models.generate_content(
        model='gemini-2.0-flash-exp',
        contents=['Could you summarize this file?', file]
    )
    print(response.text)

def test_pdf_vertex():
    import pathlib
    
    local_file_path = "D:/Documents/Programing/TRO/USside/Documents/Case Files/2023-04-18 - 1_23-cv-02421/1/IN_DC_1_23-cv-02421_2023-04-18_1_COMPLAINT_filed_by_Princess_Karibo_Filing_fee_402_.pdf"
    filepath = pathlib.Path(local_file_path)

    doc_data = Part.from_bytes(
        data=filepath.read_bytes(),  # Read bytes from the local file
        mime_type='application/pdf',
      ),

    credentials = get_gcs_credentials()
    client = Client(credentials=credentials)
    response = client.models.generate_content(
        model='gemini-2.0-flash-exp',
        contents=['Could you summarize this file?', doc_data]
    )
    print(response.text)

if __name__ == "__main__":
    import os
    from dotenv import load_dotenv
    load_dotenv()
    # test_model_names_google()
    # test_image_gen()
    # test_pdf_google_uri()
    # test_large_pdf_gemini_file_api()  # Test large PDF handling with Gemini File API (20MB-50MB)
    # vertex_genai_multi([("text", "Summurize the document: \n\n"), ("pdf_path", "D:/Documents/Programing/TRO/USside/Documents/Case Files/2023-04-18 - 1_23-cv-02421/1/IN_DC_1_23-cv-02421_2023-04-18_1_COMPLAINT_filed_by_Princess_Karibo_Filing_fee_402_.pdf")], useVertexAI=False)
    # tell_a_joke()  # Call the function to tell a joke
    # tell_a_joke()
    print(os.environ["GOOGLE_CLOUD_LOCATION"])
    start_time = time.time()
    print("gemini-pro-experimental: ", vertex_genai_text("Tell me a joke"))
    end_time = time.time()
    print(f"Time taken: {end_time - start_time:.1f} seconds")
    # print("publishers/meta/models/llama-3.2-90b-vision-instruct-maas: ", vertex_gemini_text("Tell me a joke", model_name="publishers/meta/models/llama-3.2-90b-vision-instruct-maas"))
    # print("llama-3.2-90b-vision-preview: ", vertex_llama_image_curl("What brand is this?", "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/Products/1_A_GOPRO_cropped.jpg"))
    # text = '{"102.00": "MOTION by Defendant Tititex to vacate preliminary injunction, order 65 (Gong, Zheng) (Entered: 11/11/2024)", "103.00": "NOTICE of Motion by Zheng Gong for presentment of motion to vacate 102 before Honorable Jorge L. Alonso on 11/14/2024 at 09:30 AM. (Gong, Zheng) (Entered: 11/11/2024)", "104.00": "MOTION by Plaintiff Sen Zhang to set a briefing schedule regarding Motion to Vacate Docket 102 (Neu, Abby) (Entered: 11/11/2024)", "105.00": "NOTICE of Motion by Abby Marie Neu for presentment of motion by filer to set a briefing schedule 104 before Honorable Jorge L. Alonso on 11/14/2024 at 09:30 AM. (Neu, Abby) (Entered: 11/11/2024)", "106.00": "MEMORANDUM by Tititex in support of motion to vacate 102 Preliminary Injunction (Attachments: # 1 Exhibit 1, # 2 Exhibit 2, # 3 Exhibit 3, # 4 Exhibit 4, # 5 Exhibit 5, # 6 Exhibit 6, # 7 Exhibit 7, # 8 Exhibit 8, # 9 Exhibit 9, # 10 Exhibit 10, # 11 Exhibit 11, # 12 Exhibit 12, # 13 Exhibit 13, # 14 Exhibit 14, # 15 Exhibit 15, # 16 Exhibit 16, # 17 Exhibit 17, # 18 Declaration WANG, # 19 Declaration CHEN, # 20 Exhibit A)(Gong, Zheng) (Entered: 11/11/2024)", "107.00": "REPLY by Sen Zhang to memorandum in support of motion 26 , MOTION by Plaintiff Sen Zhang for preliminary injunction 25 (Attachments: # 1 Exhibit A, # 2 Exhibit B, # 3 Exhibit C, # 4 Exhibit D, # 5 Exhibit E-1, # 6 Exhibit E-2, # 7 Exhibit E-3, # 8 Exhibit E-4, # 9 Exhibit E-5, # 10 Exhibit F, # 11 Exhibit G, # 12 Exhibit H, # 13 Exhibit I)(Mu, Shengmao) (Entered: 11/12/2024)", "108.00": "MINUTE entry before the Honorable Jorge L. Alonso: Plaintiff\'s motion for briefing schedule 104 is granted. Plaintiff shall file a response to defendant Tititex\'s motion to vacate 102 by 11/25/24. Tititex shall reply by 12/2/25. Defendant Gibelle\'s unopposed motion to set a briefing schedule 90 is granted. Gibelle shall file a response by 11/15/24. Plaintiff shall reply by 11/22/24. The motion hearing set for 11/14/24 is stricken. The Court will rule electronically on the motion to increase bond 33 35 and the motion to vacate 102 . Notice mailed by Judge\'s staff (lf, ) (Entered: 11/13/2024)", "109.00": "NOTICE of Voluntary Dismissal by Sen Zhang as to certain Defendants (Smith, Keaton) (Entered: 11/14/2024)", "110.00": "RESPONSE by GIBELLEin Opposition to MOTION by Plaintiff Sen Zhangto hold Gibelle and WITHLOC in contempt of TROs [Docket Nos. 23 and 74] 83 (Attachments: # 1 Exhibit 1, # 2 Exhibit 2, # 3 Exhibit 3, # 4 Exhibit 4, # 5 Exhibit 5, # 6 Exhibit 6, # 7 Declaration WANG, # 8 Declaration FU)(Gong, Zheng) (Entered: 11/15/2024)", "111.00": "STATEMENT of Defense by BBiggood. (Envelope postmarked 11/03/2024) (Received by mail in the Clerk\'s Office on 11/15/2024) (rc, ) (Entered: 11/15/2024)", "112.00": "REPLY by GIBELLE to MOTION by Defendant GIBELLE to vacate SEALED Order, temporary restraining order 74 and Opposition to Plaintiff\'s Motion for Preliminary Injunction 25 88 , reply to response to motion, 107 (Attachments: # 1 Exhibit A, # 2 Exhibit B, # 3 Exhibit C, # 4 Exhibit D, # 5 Declaration WANG, # 6 Declaration FU)(Gong, Zheng) (Entered: 11/18/2024)", "113.00": "ATTORNEY Appearance for Defendants MapleMay, Tigersilver by Bruce Zeason Ho Bruce Zeason (Ho, Bruce) (Entered: 11/19/2024)", "114.00": "NOTICE of Voluntary Dismissal by Sen Zhang as to certain Defendants (Smith, Keaton) (Entered: 11/21/2024)", "115.00": "SUR-REPLY by Plaintiff Sen Zhang to reply to response to motion, 112 (Mu, Shengmao) (Entered: 11/21/2024)", "116.00": "MOTION by Plaintiff Sen Zhang for leave to file SUR-REPLY RE DOCKET NO. 112 (Mu, Shengmao) (Entered: 11/21/2024)", "117.00": "NOTICE of Motion by Shengmao Mu for presentment of motion for leave to file 116 before Honorable Jorge L. Alonso on 12/3/2024 at 09:30 AM. (Mu, Shengmao) (Entered: 11/21/2024)", "118.00": "REPLY by Plaintiff Sen Zhang to response in opposition to motion, 110 , motion for miscellaneous relief 83 (Mu, Shengmao) (Entered: 11/22/2024)", "119.00": "MINUTE entry before the Honorable Jorge L. Alonso: Status hearing held. Plaintiff\'s response to Defendants\' counterclaims shall be filed within 30 days. Plaintiff\'s Motion to strike 68 is granted. Motion hearing date of 11/26/24 are stricken. Notice mailed by Judge\'s staff (lf, ) (Entered: 11/25/2024)", "120.00": "MEMORANDUM by Sen Zhang in Opposition to motion to vacate 102 (Mu, Shengmao) (Entered: 11/25/2024)", "121.00": "MINUTE entry before the Honorable Jorge L. Alonso: Telephonic motion hearing set for 12/3/24 at 9:30 a.m. Members of the public and media will be able to call in to listen to this hearing. The call-in number is ************ and the access code is 1804010308. Persons granted remote access to proceedings are reminded of the general prohibition against photographing, recording, and rebroadcasting of court proceedings. Violation of these prohibitions may result in sanctions, including removal of court issued media credentials, restricted entry to future hearings, denial of entry to future hearings, or any other sanctions deemed necessary by the Court. Notice mailed by Judge\'s staff (lf, ) (Entered: 12/02/2024)", "122.00": "REPLY by Tititex to memorandum in opposition to motion 120 (Gong, Zheng) (Entered: 12/02/2024)", "123.00": "NOTICE of Voluntary Dismissal by Sen Zhang as to a certain Defendant (Smith, Keaton) (Entered: 12/05/2024)", "124.00": "STATUS Report by Tititex, GIBELLE (Gong, Zheng) (Entered: 12/05/2024)", "125.00": "MINUTE entry before the Honorable Jorge L. Alonso: Telephonic motion hearing held. Plaintiff\'s Motion for leave to file sur-reply re Docket No. 112 116 is granted. Notice mailed by Judge\'s staff (lf, ) (Entered: 12/06/2024)", "126.00": "Notice of Settlement by Sen Zhang as to Defendants MapleMay (DOE 12) and Tigersilver (DOE 23)"}'
    
    # prompt = (
    #     "Translate the proceeding text in this legal case JSON from English to Chinese. "
    #     "Maintain the same JSON structure with step numbers as keys. "
    #     f"JSON to translate: {text}"
    # )
    
    # # response = llm_call(prompt)
    # import time
    # start_time = time.time()
    # response = vertex_gemini2_text(prompt, model_name="gemini-2.5-pro-exp-03-25")
    # end_time = time.time()
    # print(f"Time taken: {end_time - start_time:.1f} seconds")

    
    
    # print(vertex_genai_image("What is shown in this image?", "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/Products/1_A_GOPRO_cropped.jpg"))
    # print(vertex_genai_image("What is shown in this image?", "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/Products/1_A_GOPRO_cropped.jpg", model_name="gemini-2.5-pro-exp-03-25"))
    
    # print(vertex_genai_text("Tell me a joke"))  # model_name="gemini-2.5-pro-exp-03-25"
    # print(vertex_gemini2_search("What is the weather in San Francisco?"))

    # image_path1 = "D:/Documents/Programing/TRO/USside/Documents/Case Files/2024-10-17 - 1_24-cv-10653/1/US_DIS_ILND_1_24cv10653_d72133939e528_ 0_Exhibit_1/copyright/US_DIS_ILND_1_24cv10653_d72133939e528_ 0_Exhibit_1_page43_0.jpeg"
    # image_path2 = "D:/Documents/Programing/TRO/USside/Documents/Case Files/2024-10-17 - 1_24-cv-10653/1/US_DIS_ILND_1_24cv10653_d72133939e528_ 0_Exhibit_1/copyright/US_DIS_ILND_1_24cv10653_d72133939e528_ 0_Exhibit_1_page39_0.jpeg"
    # answer = vertex_genai_multi(
    #     [
    #         ("text", "This is Serge's picture:"),
    #         ("image_path", image_path1),
    #         ("text", "This is Julie's picture:"),
    #         ("image_path", image_path2),
    #         ("text", "Who has a car in it's picture?")
    #     ]
    # )
    # print(answer)
        